/*:
 * @target MZ
 * @plugindesc Elemental Reaction System v1.0.0
 * <AUTHOR>
 * @version 1.0.0
 * @description Automatic elemental reactions when combining elemental states
 *
 * @param ---Fire Reactions---
 * @text ---Fire Reactions---
 * @type string
 * @default
 *
 * @param fireIceReaction
 * @text Fire + Ice Reaction
 * @desc What happens when Fire and Ice states combine
 * @type select
 * @option Steam Explosion
 * @value steamExplosion
 * @option None
 * @value none
 * @default steamExplosion
 *
 * @param fireWindReaction
 * @text Fire + Wind Reaction
 * @desc What happens when Fire and Wind states combine
 * @type select
 * @option Inferno
 * @value inferno
 * @option None
 * @value none
 * @default inferno
 *
 * @param ---Lightning Reactions---
 * @text ---Lightning Reactions---
 * @type string
 * @default
 *
 * @param lightningWaterReaction
 * @text Lightning + Water Reaction
 * @desc What happens when Lightning and Water states combine
 * @type select
 * @option Electrocution
 * @value electrocution
 * @option None
 * @value none
 * @default electrocution
 *
 * @param reactionDamageMultiplier
 * @text Reaction Damage Multiplier
 * @desc Multiplier for reaction damage (1.0 = normal, 2.0 = double)
 * @type number
 * @decimals 1
 * @min 0.1
 * @max 5.0
 * @default 1.5
 *
 * @param ---Animation Settings---
 * @text ---Animation Settings---
 * @type string
 * @default
 *
 * @param steamExplosionAnim
 * @text Steam Explosion Animation
 * @desc Animation ID for Steam Explosion (Fire + Ice)
 * @type animation
 * @default 1
 *
 * @param electrocutionAnim
 * @text Electrocution Animation
 * @desc Animation ID for Electrocution (Lightning + Water)
 * @type animation
 * @default 2
 *
 * @param infernoAnim
 * @text Inferno Animation
 * @desc Animation ID for Inferno (Fire + Wind)
 * @type animation
 * @default 3
 *
 * @param deepFreezeAnim
 * @text Deep Freeze Animation
 * @desc Animation ID for Deep Freeze (Ice + Water)
 * @type animation
 * @default 4
 *
 * @param blizzardAnim
 * @text Blizzard Animation
 * @desc Animation ID for Blizzard (Ice + Wind)
 * @type animation
 * @default 5
 *

 *
 * @param stormAnim
 * @text Storm Animation
 * @desc Animation ID for Storm (Lightning + Wind)
 * @type animation
 * @default 7
 *
 * @param moltenRockAnim
 * @text Molten Rock Animation
 * @desc Animation ID for Molten Rock (Fire + Earth)
 * @type animation
 * @default 8
 *
 * @param sandstormAnim
 * @text Sandstorm Animation
 * @desc Animation ID for Sandstorm (Earth + Wind)
 * @type animation
 * @default 9
 *
 * @param avalancheAnim
 * @text Avalanche Animation
 * @desc Animation ID for Avalanche (Ice + Earth)
 * @type animation
 * @default 10
 *
 * @param plasmaBurstAnim
 * @text Plasma Burst Animation
 * @desc Animation ID for Plasma Burst (Fire + Lightning)
 * @type animation
 * @default 11
 *
 * @param mudslideAnim
 * @text Mudslide Animation
 * @desc Animation ID for Mudslide (Water + Earth)
 * @type animation
 * @default 12
 *
 * @param scaldingSteamAnim
 * @text Scalding Steam Animation
 * @desc Animation ID for Scalding Steam (Fire + Water)
 * @type animation
 * @default 13
 *
 * @param seismicShockAnim
 * @text Seismic Shock Animation
 * @desc Animation ID for Seismic Shock (Lightning + Earth)
 * @type animation
 * @default 14
 *
 * @help ElementalReactions.js
 * 
 * ============================================================================
 * Elemental Reaction System
 * ============================================================================
 * 
 * This plugin automatically triggers special effects when certain elemental
 * states are combined on the same target.
 * 
 * Elemental States (configure these IDs below):
 * - State 35: Burning (Fire)
 * - State 36: Freezing (Ice) 
 * - State 37: Shocking (Lightning)
 * - State 38: Soaking (Water)
 * - State 39: Shatter (Earth)
 * - State 40: Disoriented (Wind)
 * 
 * Reactions:
 * - Fire + Ice = Steam Explosion (removes both states, deals damage)
 * - Fire + Wind = Inferno (spreads burning to nearby enemies)
 * - Lightning + Water = Electrocution (massive damage + chain effect)
 * - Ice + Water = Deep Freeze (immobilizes target)
 * - And more!
 * 
 * ============================================================================
 */

(() => {
    'use strict';
    
    const parameters = PluginManager.parameters('ElementalReactions');
    const reactionDamageMultiplier = Number(parameters['reactionDamageMultiplier'] || 1.5);

    // Animation IDs
    const ANIMATION_IDS = {
        steamExplosion: Number(parameters['steamExplosionAnim'] || 1),
        electrocution: Number(parameters['electrocutionAnim'] || 2),
        inferno: Number(parameters['infernoAnim'] || 3),
        deepFreeze: Number(parameters['deepFreezeAnim'] || 4),
        blizzard: Number(parameters['blizzardAnim'] || 5),

        storm: Number(parameters['stormAnim'] || 7),
        moltenRock: Number(parameters['moltenRockAnim'] || 8),
        sandstorm: Number(parameters['sandstormAnim'] || 9),
        avalanche: Number(parameters['avalancheAnim'] || 10),
        plasmaBurst: Number(parameters['plasmaBurstAnim'] || 11),
        mudslide: Number(parameters['mudslideAnim'] || 12),
        scaldingSteam: Number(parameters['scaldingSteamAnim'] || 13),
        seismicShock: Number(parameters['seismicShockAnim'] || 14)
    };
    
    // Elemental State IDs
    const ELEMENTAL_STATES = {
        BURNING: 35,      // Fire
        FREEZING: 36,     // Ice
        SHOCKING: 37,     // Lightning
        SOAKING: 38,      // Water
        SHATTER: 39,      // Earth
        DISORIENTED: 40   // Wind
    };

    // Element ID to State mapping (based on your element list)
    const ELEMENT_TO_STATE = {
        2: ELEMENTAL_STATES.BURNING,      // Fire -> Burning
        3: ELEMENTAL_STATES.FREEZING,     // Ice -> Freezing
        4: ELEMENTAL_STATES.SHOCKING,     // Thunder -> Shocking
        5: ELEMENTAL_STATES.SOAKING,      // Water -> Soaking
        6: ELEMENTAL_STATES.SHATTER,      // Earth -> Shatter
        7: ELEMENTAL_STATES.DISORIENTED   // Wind -> Disoriented
    };
    
    // Reaction definitions
    const REACTIONS = {
        // Fire reactions
        [ELEMENTAL_STATES.BURNING]: {
            [ELEMENTAL_STATES.FREEZING]: 'steamExplosion',
            [ELEMENTAL_STATES.DISORIENTED]: 'inferno',
            [ELEMENTAL_STATES.SHATTER]: 'moltenRock',
            [ELEMENTAL_STATES.SHOCKING]: 'plasmaBurst',
            [ELEMENTAL_STATES.SOAKING]: 'scaldingSteam'
        },
        
        // Ice reactions
        [ELEMENTAL_STATES.FREEZING]: {
            [ELEMENTAL_STATES.BURNING]: 'steamExplosion',
            [ELEMENTAL_STATES.SOAKING]: 'deepFreeze',
            [ELEMENTAL_STATES.DISORIENTED]: 'blizzard',
            [ELEMENTAL_STATES.SHATTER]: 'avalanche'
        },
        
        // Lightning reactions
        [ELEMENTAL_STATES.SHOCKING]: {
            [ELEMENTAL_STATES.SOAKING]: 'electrocution',
            [ELEMENTAL_STATES.DISORIENTED]: 'storm',
            [ELEMENTAL_STATES.BURNING]: 'plasmaBurst',
            [ELEMENTAL_STATES.SHATTER]: 'seismicShock'
        },
        
        // Water reactions
        [ELEMENTAL_STATES.SOAKING]: {
            [ELEMENTAL_STATES.SHOCKING]: 'electrocution',
            [ELEMENTAL_STATES.FREEZING]: 'deepFreeze',
            [ELEMENTAL_STATES.BURNING]: 'scaldingSteam',
            [ELEMENTAL_STATES.SHATTER]: 'mudslide'
        },
        
        // Earth reactions
        [ELEMENTAL_STATES.SHATTER]: {
            [ELEMENTAL_STATES.BURNING]: 'moltenRock',
            [ELEMENTAL_STATES.DISORIENTED]: 'sandstorm',
            [ELEMENTAL_STATES.FREEZING]: 'avalanche',
            [ELEMENTAL_STATES.SHOCKING]: 'seismicShock',
            [ELEMENTAL_STATES.SOAKING]: 'mudslide'
        },
        
        // Wind reactions
        [ELEMENTAL_STATES.DISORIENTED]: {
            [ELEMENTAL_STATES.BURNING]: 'inferno',
            [ELEMENTAL_STATES.FREEZING]: 'blizzard',
            [ELEMENTAL_STATES.SHOCKING]: 'storm',
            [ELEMENTAL_STATES.SHATTER]: 'sandstorm'
        }
    };
    
    // Check for elemental reactions when a state is added
    const _Game_Battler_addState = Game_Battler.prototype.addState;
    Game_Battler.prototype.addState = function(stateId) {
        _Game_Battler_addState.call(this, stateId);

        // Check for reactions after adding the state
        if (Object.values(ELEMENTAL_STATES).includes(stateId)) {
            this.checkElementalReactions(stateId);
        }
    };

    // Hook into action execution to detect elemental spell usage
    const _Game_Action_apply = Game_Action.prototype.apply;
    Game_Action.prototype.apply = function(target) {
        // Store the attacker for damage calculations
        if (target) {
            target._lastAttacker = this.subject();
        }

        // Check for elemental reactions BEFORE applying the action
        this.checkElementalSpellReactions(target);

        // Apply the original action
        _Game_Action_apply.call(this, target);
    };

    // Check if an elemental spell triggers reactions with existing states
    Game_Action.prototype.checkElementalSpellReactions = function(target) {
        if (!this.isSkill()) return;

        const skill = this.item();
        if (!skill) return;

        // Get the element ID of this skill
        const elementId = skill.damage.elementId;
        if (!elementId || elementId === -1) return; // No element or normal attack

        // Check if this element maps to a state
        const newStateId = ELEMENT_TO_STATE[elementId];
        if (!newStateId) return; // Element doesn't map to our reaction states

        // Check if target has compatible states for reactions
        target.checkElementalSpellReaction(newStateId);
    };
    
    // Check for elemental reactions when a new state is added
    Game_Battler.prototype.checkElementalReactions = function(newStateId) {
        const reactions = REACTIONS[newStateId];
        if (!reactions) return;

        // Check each possible reaction
        for (const [otherStateId, reactionType] of Object.entries(reactions)) {
            const otherState = parseInt(otherStateId);
            if (this.isStateAffected(otherState)) {
                // Trigger the reaction
                this.triggerElementalReaction(newStateId, otherState, reactionType);
                break; // Only one reaction per state addition
            }
        }
    };

    // Check for elemental reactions when an elemental spell is used
    Game_Battler.prototype.checkElementalSpellReaction = function(incomingStateId) {
        const reactions = REACTIONS[incomingStateId];
        if (!reactions) return;

        // Check each possible reaction with existing states
        for (const [existingStateId, reactionType] of Object.entries(reactions)) {
            const existingState = parseInt(existingStateId);
            if (this.isStateAffected(existingState)) {
                // Trigger the reaction (incoming element + existing state)
                this.triggerElementalReaction(incomingStateId, existingState, reactionType);
                break; // Only one reaction per spell
            }
        }
    };
    
    // Trigger specific elemental reaction with cooldown protection
    Game_Battler.prototype.triggerElementalReaction = function(state1, state2, reactionType) {
        // Initialize reaction cooldowns if needed
        if (!this._reactionCooldowns) {
            this._reactionCooldowns = {};
        }

        // Check if this reaction is on cooldown
        const reactionKey = `${state1}_${state2}_${reactionType}`;
        const currentTurn = $gameTroop._turnCount || 0;

        if (this._reactionCooldowns[reactionKey] &&
            this._reactionCooldowns[reactionKey] >= currentTurn) {
            return; // Skip this reaction
        }

        // Set cooldown (1 turn)
        this._reactionCooldowns[reactionKey] = currentTurn + 1;

        switch (reactionType) {
            case 'steamExplosion':
                this.executeReaction_SteamExplosion(state1, state2);
                break;
            case 'electrocution':
                this.executeReaction_Electrocution(state1, state2);
                break;
            case 'inferno':
                this.executeReaction_Inferno(state1, state2);
                break;
            case 'deepFreeze':
                this.executeReaction_DeepFreeze(state1, state2);
                break;
            case 'blizzard':
                this.executeReaction_Blizzard(state1, state2);
                break;

            case 'storm':
                this.executeReaction_Storm(state1, state2);
                break;
            case 'moltenRock':
                this.executeReaction_MoltenRock(state1, state2);
                break;
            case 'sandstorm':
                this.executeReaction_Sandstorm(state1, state2);
                break;
            case 'avalanche':
                this.executeReaction_Avalanche(state1, state2);
                break;
            case 'plasmaBurst':
                this.executeReaction_PlasmaBurst(state1, state2);
                break;
            case 'mudslide':
                this.executeReaction_Mudslide(state1, state2);
                break;
            case 'scaldingSteam':
                this.executeReaction_ScaldingSteam(state1, state2);
                break;
            case 'seismicShock':
                this.executeReaction_SeismicShock(state1, state2);
                break;
        }
    };
    
    // Steam Explosion: Fire + Ice
    Game_Battler.prototype.executeReaction_SteamExplosion = function(state1, state2) {
        // Keep both states active - reactions are now a bonus, not a punishment!

        // Deal moderate steam damage - scales with MAT and target's MDF
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 50;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 10); // Minimum 10 damage
        this.gainHp(-finalDamage);

        // Show damage number immediately after the name (before spell damage)
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff6464', 'steamExplosion');
        }, 200); // Shorter delay so reaction damage appears before spell damage

        // GORGEOUS visual effects
        $gameScreen.startFlash([100, 200, 255, 200], 30); // Beautiful cyan-blue flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.steamExplosion);

        // Add screen shake for impact
        if ($gameScreen.startShake) {
            $gameScreen.startShake(8, 8, 20); // Moderate shake
        }

        // Show SEXY popup
        this.showReactionPopup('STEAM EXPLOSION!', '#ff6464', 'steamExplosion');
    };
    
    // Electrocution: Lightning + Water
    Game_Battler.prototype.executeReaction_Electrocution = function(state1, state2) {
        // Keep both states active - water still conducts, lightning still energizes!

        // Deal high electrical damage - water conducts electricity well
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 80;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.4) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 15); // Minimum 15 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffff00', 'electrocution');
        }, 200);

        // ELECTRIFYING visual effects
        $gameScreen.startFlash([255, 255, 100, 220], 35); // Intense electric flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.electrocution);

        // POWERFUL screen shake for electricity
        if ($gameScreen.startShake) {
            $gameScreen.startShake(12, 12, 25); // Strong electrical shake
        }

        // Show SEXY popup
        this.showReactionPopup('ELECTROCUTION!', '#ffff00', 'electrocution');

        // No chaining - reaction only affects this battler
    };

    
    // Inferno: Fire + Wind (spreads burning)
    Game_Battler.prototype.executeReaction_Inferno = function(state1, state2) {
        // Keep both states - wind continues to fan the flames!

        // Inferno intensifies burning - wind fans the flames
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 40;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.6) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 8); // Minimum 8 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff3200', 'inferno');
        }, 200);
        // Pure instant damage - no state effects

        // BLAZING visual effects
        $gameScreen.startFlash([255, 100, 0, 180], 40); // Intense fire flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.inferno);

        // Fiery screen shake
        if ($gameScreen.startShake) {
            $gameScreen.startShake(10, 10, 30); // Burning shake
        }

        this.showReactionPopup('INFERNO!', '#ff3200', 'inferno');
    };

    // Deep Freeze: Ice + Water (immobilizes)
    Game_Battler.prototype.executeReaction_DeepFreeze = function(state1, state2) {
        // Keep both states - frozen water is still ice and water!

        // Deal ice damage and immobilize - freezing solid
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 30;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.7) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 5); // Minimum 5 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#64c8ff', 'deepFreeze');
        }, 200);

        // Pure instant damage - no state effects

        // Visual effects
        $gameScreen.startFlash([100, 200, 255, 150], 35); // Ice blue flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.deepFreeze);

        this.showReactionPopup('DEEP FREEZE!', '#64c8ff', 'deepFreeze');
    };

    // Blizzard: Ice + Wind (AoE ice damage)
    Game_Battler.prototype.executeReaction_Blizzard = function(state1, state2) {
        // Keep both states - the blizzard continues to rage!

        // Blizzard affects only this target - strong ice storm
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 70;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 12); // Minimum 12 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#96c8ff', 'blizzard');
        }, 200);
        // Pure instant damage - no state effects

        // Visual effects
        $gameScreen.startFlash([200, 220, 255, 140], 40); // Light blue flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.blizzard);

        this.showReactionPopup('BLIZZARD!', '#96c8ff', 'blizzard');
    };



    // Storm: Lightning + Wind (chain lightning)
    Game_Battler.prototype.executeReaction_Storm = function(state1, state2) {
        // Keep both states - the storm rages on!

        // Storm affects only this target - nature's fury unleashed
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 100;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.3) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 20); // Minimum 20 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffff96', 'storm');
        }, 200);
        // Pure instant damage - no state effects

        // Visual effects
        $gameScreen.startFlash([255, 255, 200, 180], 30); // Bright white-yellow flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.storm);

        this.showReactionPopup('STORM!', '#ffff96', 'storm');
    };

    // Molten Rock: Fire + Earth (terrain damage)
    Game_Battler.prototype.executeReaction_MoltenRock = function(state1, state2) {
        // Keep both states - molten rock continues to burn and crumble!

        // Molten rock affects only this target - lava burns intensely
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 55;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 10); // Minimum 10 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff6400', 'moltenRock');
        }, 200);

        // Pure instant damage - no state effects

        // Visual effects
        $gameScreen.startFlash([255, 150, 50, 170], 35); // Orange-red flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.moltenRock);

        this.showReactionPopup('MOLTEN ROCK!', '#ff6400', 'moltenRock');
    };

    // Sandstorm: Earth + Wind (AGI debuff + DoT)
    Game_Battler.prototype.executeReaction_Sandstorm = function(state1, state2) {
        // Keep both states - the sandstorm continues to swirl!

        // Sandstorm affects only this target - blinding sand and debris
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 60;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 11); // Minimum 11 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#c89664', 'sandstorm');
        }, 200);

        // Pure instant damage - no debuff effects

        // Visual effects
        $gameScreen.startFlash([200, 150, 100, 130], 45); // Brown/tan flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.sandstorm);

        this.showReactionPopup('SANDSTORM!', '#c89664', 'sandstorm');
    };

    // Avalanche: Ice + Earth (crushing damage)
    Game_Battler.prototype.executeReaction_Avalanche = function(state1, state2) {
        // Keep both states - ice and earth combine for devastating effect!

        // Deal heavy crushing damage - ice and rock combined
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 90;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.4) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 18); // Minimum 18 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#c8c8ff', 'avalanche');
        }, 200);

        // Pure instant damage - no state effects

        // Visual effects
        $gameScreen.startFlash([200, 200, 255, 160], 40); // Light blue-white flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.avalanche);

        // Strong screen shake for avalanche
        if ($gameScreen.startShake) {
            $gameScreen.startShake(15, 15, 35); // Heavy crushing shake
        }

        this.showReactionPopup('AVALANCHE!', '#c8c8ff', 'avalanche');
    };

    // Plasma Burst: Fire + Lightning (superheated energy)
    Game_Battler.prototype.executeReaction_PlasmaBurst = function(state1, state2) {
        // Keep both states - fire and lightning create plasma!

        // Deal extreme energy damage - superheated plasma
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 120;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.2) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 25); // Minimum 25 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff96ff', 'plasmaBurst');
        }, 200);

        // Pure instant damage - no state effects

        // Visual effects
        $gameScreen.startFlash([255, 150, 255, 240], 45); // Bright magenta flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.plasmaBurst);

        // Intense screen shake for plasma burst
        if ($gameScreen.startShake) {
            $gameScreen.startShake(18, 18, 30); // Explosive energy shake
        }

        this.showReactionPopup('PLASMA BURST!', '#ff96ff', 'plasmaBurst');
    };

    // Mudslide: Water + Earth (slowing damage)
    Game_Battler.prototype.executeReaction_Mudslide = function(state1, state2) {
        // Keep both states - water and earth create muddy terrain!

        // Deal moderate earth damage - thick mud impact
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 65;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 13); // Minimum 13 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#8b6914', 'mudslide');
        }, 200);

        // Pure instant damage - no state effects

        // Visual effects
        $gameScreen.startFlash([139, 105, 20, 140], 35); // Brown muddy flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.mudslide);

        // Moderate screen shake for mudslide
        if ($gameScreen.startShake) {
            $gameScreen.startShake(10, 10, 25); // Sloshing mud shake
        }

        this.showReactionPopup('MUDSLIDE!', '#8b6914', 'mudslide');
    };

    // Scalding Steam: Fire + Water (DoT-style damage)
    Game_Battler.prototype.executeReaction_ScaldingSteam = function(state1, state2) {
        // Keep both states - fire and water create scalding steam!

        // Deal high steam damage - superheated water vapor
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 75;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.4) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 15); // Minimum 15 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff9696', 'scaldingSteam');
        }, 200);

        // Pure instant damage - no state effects

        // Visual effects
        $gameScreen.startFlash([255, 150, 150, 180], 40); // Hot pink-red flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.scaldingSteam);

        // Moderate screen shake for scalding steam
        if ($gameScreen.startShake) {
            $gameScreen.startShake(8, 8, 30); // Steam pressure shake
        }

        this.showReactionPopup('SCALDING STEAM!', '#ff9696', 'scaldingSteam');
    };

    // Seismic Shock: Lightning + Earth (ground-based electrical damage)
    Game_Battler.prototype.executeReaction_SeismicShock = function(state1, state2) {
        // Keep both states - lightning and earth create seismic disruption!

        // Deal high electrical-earth damage - ground-conducted electricity
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 85;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.3) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 17); // Minimum 17 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffcc32', 'seismicShock');
        }, 200);

        // Pure instant damage - no state effects

        // Visual effects
        $gameScreen.startFlash([255, 204, 50, 200], 35); // Golden electrical flash
        $gameTemp.requestAnimation([this], ANIMATION_IDS.seismicShock);

        // Strong screen shake for seismic shock
        if ($gameScreen.startShake) {
            $gameScreen.startShake(14, 14, 40); // Earthquake-like shake
        }

        this.showReactionPopup('SEISMIC SHOCK!', '#ffcc32', 'seismicShock');
    };

    // Show reaction popup using the built-in VisuMZ system (single method only)
    Game_Battler.prototype.showReactionPopup = function(text, color, reactionType) {
        // Use only ONE method to avoid duplicates
        if (this.setupTextPopup) {
            const popupSettings = {
                textColor: color,
                flashColor: [255, 255, 255, 200],
                flashDuration: 45
            };
            this.setupTextPopup(text, popupSettings);
        } else if (SceneManager._scene && SceneManager._scene._spriteset) {
            // Fallback: try through sprite only if battler method doesn't exist
            const sprites = SceneManager._scene._spriteset.battlerSprites();
            const sprite = sprites.find(s => s._battler === this);
            if (sprite && sprite.setupTextPopup) {
                const popupSettings = {
                    textColor: color,
                    flashColor: [255, 255, 255, 200],
                    flashDuration: 45
                };
                sprite.setupTextPopup(text, popupSettings);
            }
        }
    };





    // Clear reaction cooldowns when battle ends
    const _BattleManager_endBattle = BattleManager.endBattle;
    BattleManager.endBattle = function(result) {
        // Clear all reaction cooldowns for all battlers
        $gameParty.allMembers().forEach(actor => {
            if (actor._reactionCooldowns) {
                actor._reactionCooldowns = {};
            }
        });

        $gameTroop.members().forEach(enemy => {
            if (enemy._reactionCooldowns) {
                enemy._reactionCooldowns = {};
            }
        });

        _BattleManager_endBattle.call(this, result);
    };

})();
