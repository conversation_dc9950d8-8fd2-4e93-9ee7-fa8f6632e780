/*:
 * @target MZ
 * @plugindesc Elemental Reaction System v1.0.0
 * <AUTHOR>
 * @version 1.0.0
 * @description Automatic elemental reactions when combining elemental states
 *
 * @param ---Fire Reactions---
 * @text ---Fire Reactions---
 * @type string
 * @default
 *
 * @param fireIceReaction
 * @text Fire + Ice Reaction
 * @desc What happens when Fire and Ice states combine
 * @type select
 * @option Steam Explosion
 * @value steamExplosion
 * @option None
 * @value none
 * @default steamExplosion
 *
 * @param fireWindReaction
 * @text Fire + Wind Reaction
 * @desc What happens when Fire and Wind states combine
 * @type select
 * @option Inferno
 * @value inferno
 * @option None
 * @value none
 * @default inferno
 *
 * @param ---Lightning Reactions---
 * @text ---Lightning Reactions---
 * @type string
 * @default
 *
 * @param lightningWaterReaction
 * @text Lightning + Water Reaction
 * @desc What happens when Lightning and Water states combine
 * @type select
 * @option Electrocution
 * @value electrocution
 * @option None
 * @value none
 * @default electrocution
 *
 * @param reactionDamageMultiplier
 * @text Reaction Damage Multiplier
 * @desc Multiplier for reaction damage (1.0 = normal, 2.0 = double)
 * @type number
 * @decimals 1
 * @min 0.1
 * @max 5.0
 * @default 1.5
 *
 * @param removeStatesOnReaction
 * @text Remove States on Reaction
 * @desc Remove both elemental states when reaction triggers (longer cooldown)
 * @type boolean
 * @default false
 *
 * @param reactionCooldownActions
 * @text Reaction Cooldown (Actions)
 * @desc Number of actions before same reaction can trigger again
 * @type number
 * @min 1
 * @max 10
 * @default 3
 *
 * @param ---Animation Settings---
 * @text ---Animation Settings---
 * @type string
 * @default
 *
 * @param steamExplosionAnim
 * @text Steam Explosion Animation
 * @desc Animation ID for Steam Explosion (Fire + Ice)
 * @type animation
 * @default 1
 *
 * @param electrocutionAnim
 * @text Electrocution Animation
 * @desc Animation ID for Electrocution (Lightning + Water)
 * @type animation
 * @default 2
 *
 * @param infernoAnim
 * @text Inferno Animation
 * @desc Animation ID for Inferno (Fire + Wind)
 * @type animation
 * @default 3
 *
 * @param deepFreezeAnim
 * @text Deep Freeze Animation
 * @desc Animation ID for Deep Freeze (Ice + Water)
 * @type animation
 * @default 4
 *
 * @param blizzardAnim
 * @text Blizzard Animation
 * @desc Animation ID for Blizzard (Ice + Wind)
 * @type animation
 * @default 5
 *

 *
 * @param stormAnim
 * @text Storm Animation
 * @desc Animation ID for Storm (Lightning + Wind)
 * @type animation
 * @default 7
 *
 * @param moltenRockAnim
 * @text Molten Rock Animation
 * @desc Animation ID for Molten Rock (Fire + Earth)
 * @type animation
 * @default 8
 *
 * @param sandstormAnim
 * @text Sandstorm Animation
 * @desc Animation ID for Sandstorm (Earth + Wind)
 * @type animation
 * @default 9
 *
 * @param avalancheAnim
 * @text Avalanche Animation
 * @desc Animation ID for Avalanche (Ice + Earth)
 * @type animation
 * @default 10
 *
 * @param plasmaBurstAnim
 * @text Plasma Burst Animation
 * @desc Animation ID for Plasma Burst (Fire + Lightning)
 * @type animation
 * @default 11
 *
 * @param mudslideAnim
 * @text Mudslide Animation
 * @desc Animation ID for Mudslide (Water + Earth)
 * @type animation
 * @default 12
 *
 * @param scaldingSteamAnim
 * @text Scalding Steam Animation
 * @desc Animation ID for Scalding Steam (Fire + Water)
 * @type animation
 * @default 13
 *
 * @param seismicShockAnim
 * @text Seismic Shock Animation
 * @desc Animation ID for Seismic Shock (Lightning + Earth)
 * @type animation
 * @default 14
 *
 * @help ElementalReactions.js
 * 
 * ============================================================================
 * Elemental Reaction System
 * ============================================================================
 * 
 * This plugin automatically triggers special effects when certain elemental
 * states are combined on the same target.
 * 
 * Elemental States (configure these IDs below):
 * - State 35: Burning (Fire)
 * - State 36: Freezing (Ice) 
 * - State 37: Shocking (Lightning)
 * - State 38: Soaking (Water)
 * - State 39: Shatter (Earth)
 * - State 40: Disoriented (Wind)
 * 
 * Reactions (13 total - all instant damage effects):
 * - Fire + Ice = Steam Explosion
 * - Fire + Wind = Inferno
 * - Fire + Earth = Molten Rock
 * - Fire + Lightning = Plasma Burst
 * - Fire + Water = Scalding Steam
 * - Lightning + Water = Electrocution
 * - Lightning + Wind = Storm
 * - Lightning + Earth = Seismic Shock
 * - Ice + Water = Deep Freeze
 * - Ice + Wind = Blizzard
 * - Ice + Earth = Avalanche
 * - Water + Earth = Mudslide
 * - Earth + Wind = Sandstorm
 * 
 * ============================================================================
 */

(() => {
    'use strict';
    
    const parameters = PluginManager.parameters('ElementalReactions');
    const reactionDamageMultiplier = Number(parameters['reactionDamageMultiplier'] || 1.5);
    const removeStatesOnReaction = parameters['removeStatesOnReaction'] === 'true';
    const reactionCooldownActions = Number(parameters['reactionCooldownActions'] || 3);

    // Animation IDs
    const ANIMATION_IDS = {
        steamExplosion: Number(parameters['steamExplosionAnim'] || 1),
        electrocution: Number(parameters['electrocutionAnim'] || 2),
        inferno: Number(parameters['infernoAnim'] || 3),
        deepFreeze: Number(parameters['deepFreezeAnim'] || 4),
        blizzard: Number(parameters['blizzardAnim'] || 5),

        storm: Number(parameters['stormAnim'] || 7),
        moltenRock: Number(parameters['moltenRockAnim'] || 8),
        sandstorm: Number(parameters['sandstormAnim'] || 9),
        avalanche: Number(parameters['avalancheAnim'] || 10),
        plasmaBurst: Number(parameters['plasmaBurstAnim'] || 11),
        mudslide: Number(parameters['mudslideAnim'] || 12),
        scaldingSteam: Number(parameters['scaldingSteamAnim'] || 13),
        seismicShock: Number(parameters['seismicShockAnim'] || 14)
    };
    
    // Elemental State IDs
    const ELEMENTAL_STATES = {
        BURNING: 35,      // Fire
        FREEZING: 36,     // Ice
        SHOCKING: 37,     // Lightning
        SOAKING: 38,      // Water
        SHATTER: 39,      // Earth
        DISORIENTED: 40   // Wind
    };

    // Element ID to State mapping (based on your element list)
    const ELEMENT_TO_STATE = {
        2: ELEMENTAL_STATES.BURNING,      // Fire -> Burning
        3: ELEMENTAL_STATES.FREEZING,     // Ice -> Freezing
        4: ELEMENTAL_STATES.SHOCKING,     // Thunder -> Shocking
        5: ELEMENTAL_STATES.SOAKING,      // Water -> Soaking
        6: ELEMENTAL_STATES.SHATTER,      // Earth -> Shatter
        7: ELEMENTAL_STATES.DISORIENTED   // Wind -> Disoriented
    };
    
    // Reaction definitions
    const REACTIONS = {
        // Fire reactions
        [ELEMENTAL_STATES.BURNING]: {
            [ELEMENTAL_STATES.FREEZING]: 'steamExplosion',
            [ELEMENTAL_STATES.DISORIENTED]: 'inferno',
            [ELEMENTAL_STATES.SHATTER]: 'moltenRock',
            [ELEMENTAL_STATES.SHOCKING]: 'plasmaBurst',
            [ELEMENTAL_STATES.SOAKING]: 'scaldingSteam'
        },
        
        // Ice reactions
        [ELEMENTAL_STATES.FREEZING]: {
            [ELEMENTAL_STATES.BURNING]: 'steamExplosion',
            [ELEMENTAL_STATES.SOAKING]: 'deepFreeze',
            [ELEMENTAL_STATES.DISORIENTED]: 'blizzard',
            [ELEMENTAL_STATES.SHATTER]: 'avalanche'
        },
        
        // Lightning reactions
        [ELEMENTAL_STATES.SHOCKING]: {
            [ELEMENTAL_STATES.SOAKING]: 'electrocution',
            [ELEMENTAL_STATES.DISORIENTED]: 'storm',
            [ELEMENTAL_STATES.BURNING]: 'plasmaBurst',
            [ELEMENTAL_STATES.SHATTER]: 'seismicShock'
        },
        
        // Water reactions
        [ELEMENTAL_STATES.SOAKING]: {
            [ELEMENTAL_STATES.SHOCKING]: 'electrocution',
            [ELEMENTAL_STATES.FREEZING]: 'deepFreeze',
            [ELEMENTAL_STATES.BURNING]: 'scaldingSteam',
            [ELEMENTAL_STATES.SHATTER]: 'mudslide'
        },
        
        // Earth reactions
        [ELEMENTAL_STATES.SHATTER]: {
            [ELEMENTAL_STATES.BURNING]: 'moltenRock',
            [ELEMENTAL_STATES.DISORIENTED]: 'sandstorm',
            [ELEMENTAL_STATES.FREEZING]: 'avalanche',
            [ELEMENTAL_STATES.SHOCKING]: 'seismicShock',
            [ELEMENTAL_STATES.SOAKING]: 'mudslide'
        },
        
        // Wind reactions
        [ELEMENTAL_STATES.DISORIENTED]: {
            [ELEMENTAL_STATES.BURNING]: 'inferno',
            [ELEMENTAL_STATES.FREEZING]: 'blizzard',
            [ELEMENTAL_STATES.SHOCKING]: 'storm',
            [ELEMENTAL_STATES.SHATTER]: 'sandstorm'
        }
    };
    
    // Check for elemental reactions when a state is added
    const _Game_Battler_addState = Game_Battler.prototype.addState;
    Game_Battler.prototype.addState = function(stateId) {
        _Game_Battler_addState.call(this, stateId);

        // Check for reactions after adding the state
        if (Object.values(ELEMENTAL_STATES).includes(stateId)) {
            this.checkElementalReactions(stateId);
        }
    };

    // Hook into action execution to detect elemental spell usage
    const _Game_Action_apply = Game_Action.prototype.apply;
    Game_Action.prototype.apply = function(target) {
        // Increment global action counter for cooldown system
        if (!$gameTemp._globalActionCount) {
            $gameTemp._globalActionCount = 0;
        }
        $gameTemp._globalActionCount++;

        // Store the attacker for damage calculations
        if (target) {
            target._lastAttacker = this.subject();
        }

        // Check for elemental reactions BEFORE applying the action
        this.checkElementalSpellReactions(target);

        // Apply the original action
        _Game_Action_apply.call(this, target);
    };

    // Check if an elemental spell triggers reactions with existing states
    Game_Action.prototype.checkElementalSpellReactions = function(target) {
        if (!this.isSkill()) return;

        const skill = this.item();
        if (!skill) return;

        // Get the element ID of this skill
        const elementId = skill.damage.elementId;
        if (!elementId || elementId === -1) return; // No element or normal attack

        // Check if this element maps to a state
        const newStateId = ELEMENT_TO_STATE[elementId];
        if (!newStateId) return; // Element doesn't map to our reaction states

        // Check if target has compatible states for reactions
        target.checkElementalSpellReaction(newStateId);
    };
    
    // Check for elemental reactions when a new state is added
    Game_Battler.prototype.checkElementalReactions = function(newStateId) {
        const reactions = REACTIONS[newStateId];
        if (!reactions) return;

        // Check each possible reaction
        for (const [otherStateId, reactionType] of Object.entries(reactions)) {
            const otherState = parseInt(otherStateId);
            if (this.isStateAffected(otherState)) {
                // Trigger the reaction
                this.triggerElementalReaction(newStateId, otherState, reactionType);
                break; // Only one reaction per state addition
            }
        }
    };

    // Check for elemental reactions when an elemental spell is used
    Game_Battler.prototype.checkElementalSpellReaction = function(incomingStateId) {
        const reactions = REACTIONS[incomingStateId];
        if (!reactions) return;

        // Check each possible reaction with existing states
        for (const [existingStateId, reactionType] of Object.entries(reactions)) {
            const existingState = parseInt(existingStateId);
            if (this.isStateAffected(existingState)) {
                // Trigger the reaction (incoming element + existing state)
                this.triggerElementalReaction(incomingStateId, existingState, reactionType);
                break; // Only one reaction per spell
            }
        }
    };
    
    // Trigger specific elemental reaction with cooldown protection
    Game_Battler.prototype.triggerElementalReaction = function(state1, state2, reactionType) {
        // Initialize reaction cooldowns if needed
        if (!this._reactionCooldowns) {
            this._reactionCooldowns = {};
        }

        // Initialize global action counter if needed
        if (!$gameTemp._globalActionCount) {
            $gameTemp._globalActionCount = 0;
        }

        // Check if this reaction is on cooldown (3 actions instead of 1 turn)
        const reactionKey = `${state1}_${state2}_${reactionType}`;
        const currentActionCount = $gameTemp._globalActionCount;

        if (this._reactionCooldowns[reactionKey] &&
            this._reactionCooldowns[reactionKey] > currentActionCount) {
            return; // Skip this reaction
        }

        // Set cooldown (configurable actions from now)
        this._reactionCooldowns[reactionKey] = currentActionCount + reactionCooldownActions;

        // Remove states if configured to do so
        if (removeStatesOnReaction) {
            this.removeState(state1);
            this.removeState(state2);
        }

        switch (reactionType) {
            case 'steamExplosion':
                this.executeReaction_SteamExplosion(state1, state2);
                break;
            case 'electrocution':
                this.executeReaction_Electrocution(state1, state2);
                break;
            case 'inferno':
                this.executeReaction_Inferno(state1, state2);
                break;
            case 'deepFreeze':
                this.executeReaction_DeepFreeze(state1, state2);
                break;
            case 'blizzard':
                this.executeReaction_Blizzard(state1, state2);
                break;

            case 'storm':
                this.executeReaction_Storm(state1, state2);
                break;
            case 'moltenRock':
                this.executeReaction_MoltenRock(state1, state2);
                break;
            case 'sandstorm':
                this.executeReaction_Sandstorm(state1, state2);
                break;
            case 'avalanche':
                this.executeReaction_Avalanche(state1, state2);
                break;
            case 'plasmaBurst':
                this.executeReaction_PlasmaBurst(state1, state2);
                break;
            case 'mudslide':
                this.executeReaction_Mudslide(state1, state2);
                break;
            case 'scaldingSteam':
                this.executeReaction_ScaldingSteam(state1, state2);
                break;
            case 'seismicShock':
                this.executeReaction_SeismicShock(state1, state2);
                break;
        }
    };
    
    // Steam Explosion: Fire + Ice - Heals attacker from steam energy
    Game_Battler.prototype.executeReaction_SteamExplosion = function(state1, state2) {
        // Steam explosion converts thermal energy - attacker gains health!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 50;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 10);
        this.gainHp(-finalDamage);

        // UNIQUE: Heal the attacker for 30% of damage dealt (steam energy absorption)
        if (this._lastAttacker && this._lastAttacker.isAlive()) {
            const healAmount = Math.floor(finalDamage * 0.3);
            this._lastAttacker.gainHp(healAmount);

            // Show heal popup on attacker
            setTimeout(() => {
                if (this._lastAttacker.showReactionPopup) {
                    this._lastAttacker.showReactionPopup(`+${healAmount}`, '#64ff64', 'heal');
                }
            }, 400);
        }

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff6464', 'steamExplosion');
        }, 200);

        $gameScreen.startFlash([100, 200, 255, 200], 30);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.steamExplosion);
        if ($gameScreen.startShake) {
            $gameScreen.startShake(8, 8, 20);
        }

        this.showReactionPopup('STEAM EXPLOSION!', '#ff6464', 'steamExplosion');
    };
    
    // Electrocution: Lightning + Water - Drains TP completely
    Game_Battler.prototype.executeReaction_Electrocution = function(state1, state2) {
        // Electrical shock disrupts energy flow - drains all TP!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 80;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.4) * reactionDamageMultiplier);
        let finalDamage = Math.max(damage, 15);

        // UNIQUE: Double damage if target has low HP (electricity conducts better through weakened bodies)
        if (this.hp < this.mhp * 0.3) { // If below 30% HP
            finalDamage = Math.floor(finalDamage * 2);
            setTimeout(() => {
                this.showReactionPopup(`CRITICAL SHOCK!`, '#ff6464', 'criticalShock');
            }, 600);
        }

        this.gainHp(-finalDamage);

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffff00', 'electrocution');
        }, 200);

        $gameScreen.startFlash([255, 255, 100, 220], 35);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.electrocution);
        if ($gameScreen.startShake) {
            $gameScreen.startShake(12, 12, 25);
        }

        this.showReactionPopup('ELECTROCUTION!', '#ffff00', 'electrocution');
    };

    
    // Inferno: Fire + Wind - Spreads to random nearby enemy
    Game_Battler.prototype.executeReaction_Inferno = function(state1, state2) {
        // Wind spreads fire - hits this target plus one random enemy!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 40;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.6) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 8);
        this.gainHp(-finalDamage);

        // UNIQUE: Spread fire to one random other enemy for 50% damage
        if (this.isEnemy()) {
            const otherEnemies = $gameTroop.members().filter(enemy =>
                enemy !== this && enemy.isAlive()
            );
            if (otherEnemies.length > 0) {
                const randomEnemy = otherEnemies[Math.floor(Math.random() * otherEnemies.length)];
                const spreadDamage = Math.floor(finalDamage * 0.5);
                randomEnemy.gainHp(-spreadDamage);

                setTimeout(() => {
                    if (randomEnemy.showReactionPopup) {
                        randomEnemy.showReactionPopup(`${spreadDamage}`, '#ff6400', 'spread');
                    }
                }, 400);
            }
        }

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff3200', 'inferno');
        }, 200);

        $gameScreen.startFlash([255, 100, 0, 180], 40);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.inferno);
        if ($gameScreen.startShake) {
            $gameScreen.startShake(10, 10, 30);
        }

        this.showReactionPopup('INFERNO!', '#ff3200', 'inferno');
    };

    // Deep Freeze: Ice + Water - Damage scales with missing HP
    Game_Battler.prototype.executeReaction_DeepFreeze = function(state1, state2) {
        // Ice crystallizes in wounds - more damage to injured targets!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 30;
        const defense = this.mdf;

        // UNIQUE: Damage increases based on missing HP (ice forms in wounds)
        const hpPercent = this.hp / this.mhp;
        const missingHpBonus = Math.floor((1 - hpPercent) * 60); // Up to +60 damage when near death
        const totalBaseDamage = baseDamage + missingHpBonus;

        const damage = Math.floor((totalBaseDamage - defense * 0.7) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 5);
        this.gainHp(-finalDamage);

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#64c8ff', 'deepFreeze');
        }, 200);

        // Show bonus damage indicator if significant
        if (missingHpBonus > 10) {
            setTimeout(() => {
                this.showReactionPopup(`CRITICAL FREEZE!`, '#96e6ff', 'criticalFreeze');
            }, 500);
        }

        $gameScreen.startFlash([100, 200, 255, 150], 35);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.deepFreeze);

        this.showReactionPopup('DEEP FREEZE!', '#64c8ff', 'deepFreeze');
    };

    // Blizzard: Ice + Wind - Hits all enemies for reduced damage
    Game_Battler.prototype.executeReaction_Blizzard = function(state1, state2) {
        // Wind spreads ice everywhere - hits ALL enemies!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 45; // Reduced base
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 8);
        this.gainHp(-finalDamage);

        // UNIQUE: Hit all OTHER enemies for 40% damage (blizzard spreads)
        if (this.isEnemy()) {
            const otherEnemies = $gameTroop.members().filter(enemy =>
                enemy !== this && enemy.isAlive()
            );
            otherEnemies.forEach(enemy => {
                const blizzardDamage = Math.floor(finalDamage * 0.4);
                enemy.gainHp(-blizzardDamage);

                setTimeout(() => {
                    if (enemy.showReactionPopup) {
                        enemy.showReactionPopup(`${blizzardDamage}`, '#c8e6ff', 'blizzardSpread');
                    }
                }, 300 + Math.random() * 200); // Staggered timing
            });
        }

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#96c8ff', 'blizzard');
        }, 200);

        $gameScreen.startFlash([200, 220, 255, 140], 40);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.blizzard);

        this.showReactionPopup('BLIZZARD!', '#96c8ff', 'blizzard');
    };



    // Storm: Lightning + Wind - AGI debuff + damage
    Game_Battler.prototype.executeReaction_Storm = function(state1, state2) {
        // Storm disrupts movement and coordination!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 60;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.3) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 20);
        this.gainHp(-finalDamage);

        // UNIQUE: Permanent AGI debuff for remainder of battle (stacks)
        const agiDebuff = Math.floor(this.params[6] * 0.15); // 15% of base AGI
        this.addParam(6, -agiDebuff); // Reduce AGI (index 6)

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffff96', 'storm');
        }, 200);

        // Show AGI debuff indicator
        setTimeout(() => {
            this.showReactionPopup(`AGI -${agiDebuff}!`, '#ffcc00', 'agiDebuff');
        }, 500);

        $gameScreen.startFlash([255, 255, 200, 180], 30);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.storm);

        this.showReactionPopup('STORM!', '#ffff96', 'storm');
    };

    // Molten Rock: Fire + Earth - Damage increases with attacker's current HP
    Game_Battler.prototype.executeReaction_MoltenRock = function(state1, state2) {
        // Healthy casters create hotter, more destructive molten rock!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 35;
        const defense = this.mdf;

        // UNIQUE: Damage scales with attacker's current HP% (healthier = hotter lava)
        const attacker = this._lastAttacker || this;
        const hpPercent = attacker.hp / attacker.mhp;
        const healthBonus = Math.floor(hpPercent * 50); // Up to +50 damage at full HP
        const totalBaseDamage = baseDamage + healthBonus;

        const damage = Math.floor((totalBaseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 10);
        this.gainHp(-finalDamage);

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff6400', 'moltenRock');
        }, 200);

        // Show health power indicator if significant bonus
        if (healthBonus > 25) {
            setTimeout(() => {
                this.showReactionPopup(`SUPERHEATED!`, '#ffaa00', 'superheated');
            }, 500);
        }

        $gameScreen.startFlash([255, 150, 50, 170], 35);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.moltenRock);

        this.showReactionPopup('MOLTEN ROCK!', '#ff6400', 'moltenRock');
    };

    // Sandstorm: Earth + Wind - Damage based on battle turn number
    Game_Battler.prototype.executeReaction_Sandstorm = function(state1, state2) {
        // Sandstorm intensifies as battle drags on - longer fights = more debris!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 30;
        const defense = this.mdf;

        // UNIQUE: Damage increases with battle turn count (sandstorm builds over time)
        const turnCount = $gameTroop._turnCount || 1;
        const turnBonus = Math.min(turnCount * 8, 80); // +8 per turn, max +80
        const totalBaseDamage = baseDamage + turnBonus;

        const damage = Math.floor((totalBaseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 11);
        this.gainHp(-finalDamage);

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#c89664', 'sandstorm');
        }, 200);

        // Show turn power indicator if significant bonus
        if (turnBonus > 30) {
            setTimeout(() => {
                this.showReactionPopup(`RAGING STORM!`, '#d4af37', 'ragingStorm');
            }, 500);
        }

        $gameScreen.startFlash([200, 150, 100, 130], 45);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.sandstorm);

        this.showReactionPopup('SANDSTORM!', '#c89664', 'sandstorm');
    };

    // Avalanche: Ice + Earth - Damage scales with target's DEF
    Game_Battler.prototype.executeReaction_Avalanche = function(state1, state2) {
        // Heavy armor gets crushed harder by avalanche weight!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 60;

        // UNIQUE: More damage to heavily armored targets (avalanche crushes armor)
        const defBonus = Math.floor(this.def * 0.8); // DEF becomes a weakness
        const totalBaseDamage = baseDamage + defBonus;

        const damage = Math.floor(totalBaseDamage * reactionDamageMultiplier); // No MDF reduction - physical crushing
        const finalDamage = Math.max(damage, 18);
        this.gainHp(-finalDamage);

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#c8c8ff', 'avalanche');
        }, 200);

        // Show armor crush indicator if significant DEF bonus
        if (defBonus > 15) {
            setTimeout(() => {
                this.showReactionPopup(`ARMOR CRUSHED!`, '#ff9696', 'armorCrush');
            }, 500);
        }

        $gameScreen.startFlash([200, 200, 255, 160], 40);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.avalanche);
        if ($gameScreen.startShake) {
            $gameScreen.startShake(15, 15, 35);
        }

        this.showReactionPopup('AVALANCHE!', '#c8c8ff', 'avalanche');
    };

    // Plasma Burst: Fire + Lightning - Ignores all magical defense
    Game_Battler.prototype.executeReaction_PlasmaBurst = function(state1, state2) {
        // Plasma energy bypasses magical defenses completely!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 85;

        // UNIQUE: Ignores MDF completely (plasma burns through magical barriers)
        const damage = Math.floor(baseDamage * reactionDamageMultiplier); // No defense reduction
        const finalDamage = Math.max(damage, 25);
        this.gainHp(-finalDamage);

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff96ff', 'plasmaBurst');
        }, 200);

        // Show defense bypass indicator
        setTimeout(() => {
            this.showReactionPopup(`DEFENSE BYPASSED!`, '#ffff96', 'bypass');
        }, 500);

        $gameScreen.startFlash([255, 150, 255, 240], 45);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.plasmaBurst);
        if ($gameScreen.startShake) {
            $gameScreen.startShake(18, 18, 30);
        }

        this.showReactionPopup('PLASMA BURST!', '#ff96ff', 'plasmaBurst');
    };

    // Mudslide: Water + Earth - More damage to fast enemies
    Game_Battler.prototype.executeReaction_Mudslide = function(state1, state2) {
        // Fast enemies get hit harder by sudden mud - momentum works against them!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 45;
        const defense = this.mdf;

        // UNIQUE: Damage scales with target's AGI (faster = more mud damage)
        const agiBonus = Math.floor(this.agi * 0.6); // AGI becomes a weakness
        const totalBaseDamage = baseDamage + agiBonus;

        const damage = Math.floor((totalBaseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 13);
        this.gainHp(-finalDamage);

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#8b6914', 'mudslide');
        }, 200);

        // Show speed penalty indicator if significant AGI bonus
        if (agiBonus > 12) {
            setTimeout(() => {
                this.showReactionPopup(`MOMENTUM TRAP!`, '#d4af37', 'momentumTrap');
            }, 500);
        }

        $gameScreen.startFlash([139, 105, 20, 140], 35);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.mudslide);
        if ($gameScreen.startShake) {
            $gameScreen.startShake(10, 10, 25);
        }

        this.showReactionPopup('MUDSLIDE!', '#8b6914', 'mudslide');
    };

    // Scalding Steam: Fire + Water - Damage increases if target recently took damage
    Game_Battler.prototype.executeReaction_ScaldingSteam = function(state1, state2) {
        // Steam finds open wounds - more damage to recently injured targets!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 50;
        const defense = this.mdf;

        // UNIQUE: Bonus damage if target took damage this turn (steam enters wounds)
        let woundBonus = 0;
        if (this._damageTakenThisTurn && this._damageTakenThisTurn > 0) {
            woundBonus = Math.floor(this._damageTakenThisTurn * 0.3); // 30% of damage taken this turn
        }
        const totalBaseDamage = baseDamage + woundBonus;

        const damage = Math.floor((totalBaseDamage - defense * 0.4) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 15);
        this.gainHp(-finalDamage);

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff9696', 'scaldingSteam');
        }, 200);

        // Show wound exploitation indicator if significant bonus
        if (woundBonus > 10) {
            setTimeout(() => {
                this.showReactionPopup(`WOUND SEARED!`, '#ff6464', 'woundSeared');
            }, 500);
        }

        $gameScreen.startFlash([255, 150, 150, 180], 40);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.scaldingSteam);
        if ($gameScreen.startShake) {
            $gameScreen.startShake(8, 8, 30);
        }

        this.showReactionPopup('SCALDING STEAM!', '#ff9696', 'scaldingSteam');
    };

    // Seismic Shock: Lightning + Earth - LUK debuff + damage
    Game_Battler.prototype.executeReaction_SeismicShock = function(state1, state2) {
        // Seismic disruption affects fortune and precision!
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 55;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.3) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 17);
        this.gainHp(-finalDamage);

        // UNIQUE: Permanent LUK debuff for remainder of battle (stacks)
        const lukDebuff = Math.floor(this.params[7] * 0.12); // 12% of base LUK
        this.addParam(7, -lukDebuff); // Reduce LUK (index 7)

        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffcc32', 'seismicShock');
        }, 200);

        // Show LUK debuff indicator
        setTimeout(() => {
            this.showReactionPopup(`LUK -${lukDebuff}!`, '#ffd700', 'lukDebuff');
        }, 500);

        $gameScreen.startFlash([255, 204, 50, 200], 35);
        $gameTemp.requestAnimation([this], ANIMATION_IDS.seismicShock);
        if ($gameScreen.startShake) {
            $gameScreen.startShake(14, 14, 40);
        }

        this.showReactionPopup('SEISMIC SHOCK!', '#ffcc32', 'seismicShock');
    };

    // Show reaction popup using the built-in VisuMZ system (single method only)
    Game_Battler.prototype.showReactionPopup = function(text, color, reactionType) {
        // Use only ONE method to avoid duplicates
        if (this.setupTextPopup) {
            const popupSettings = {
                textColor: color,
                flashColor: [255, 255, 255, 200],
                flashDuration: 45
            };
            this.setupTextPopup(text, popupSettings);
        } else if (SceneManager._scene && SceneManager._scene._spriteset) {
            // Fallback: try through sprite only if battler method doesn't exist
            const sprites = SceneManager._scene._spriteset.battlerSprites();
            const sprite = sprites.find(s => s._battler === this);
            if (sprite && sprite.setupTextPopup) {
                const popupSettings = {
                    textColor: color,
                    flashColor: [255, 255, 255, 200],
                    flashDuration: 45
                };
                sprite.setupTextPopup(text, popupSettings);
            }
        }
    };





    // Clear reaction cooldowns when battle ends
    const _BattleManager_endBattle = BattleManager.endBattle;
    BattleManager.endBattle = function(result) {
        // Clear all reaction cooldowns for all battlers
        $gameParty.allMembers().forEach(actor => {
            if (actor._reactionCooldowns) {
                actor._reactionCooldowns = {};
            }
        });

        $gameTroop.members().forEach(enemy => {
            if (enemy._reactionCooldowns) {
                enemy._reactionCooldowns = {};
            }
        });

        // Reset global action counter
        if ($gameTemp._globalActionCount) {
            $gameTemp._globalActionCount = 0;
        }

        _BattleManager_endBattle.call(this, result);
    };

})();
