/*:
 * @target MZ
 * @plugindesc Elemental Reaction System v1.0.0
 * <AUTHOR>
 * @version 1.0.0
 * @description Automatic elemental reactions when combining elemental states
 *
 * @param ---Fire Reactions---
 * @text ---Fire Reactions---
 * @type string
 * @default
 *
 * @param fireIceReaction
 * @text Fire + Ice Reaction
 * @desc What happens when Fire and Ice states combine
 * @type select
 * @option Steam Explosion
 * @value steamExplosion
 * @option None
 * @value none
 * @default steamExplosion
 *
 * @param fireWindReaction
 * @text Fire + Wind Reaction
 * @desc What happens when Fire and Wind states combine
 * @type select
 * @option Inferno
 * @value inferno
 * @option None
 * @value none
 * @default inferno
 *
 * @param ---Lightning Reactions---
 * @text ---Lightning Reactions---
 * @type string
 * @default
 *
 * @param lightningWaterReaction
 * @text Lightning + Water Reaction
 * @desc What happens when Lightning and Water states combine
 * @type select
 * @option Electrocution
 * @value electrocution
 * @option None
 * @value none
 * @default electrocution
 *
 * @param reactionDamageMultiplier
 * @text Reaction Damage Multiplier
 * @desc Multiplier for reaction damage (1.0 = normal, 2.0 = double)
 * @type number
 * @decimals 1
 * @min 0.1
 * @max 5.0
 * @default 1.5
 *
 * @param ---Animation Settings---
 * @text ---Animation Settings---
 * @type string
 * @default
 *
 * @param steamExplosionAnim
 * @text Steam Explosion Animation
 * @desc Animation ID for Steam Explosion (Fire + Ice)
 * @type animation
 * @default 1
 *
 * @param electrocutionAnim
 * @text Electrocution Animation
 * @desc Animation ID for Electrocution (Lightning + Water)
 * @type animation
 * @default 2
 *
 * @param infernoAnim
 * @text Inferno Animation
 * @desc Animation ID for Inferno (Fire + Wind)
 * @type animation
 * @default 3
 *
 * @param deepFreezeAnim
 * @text Deep Freeze Animation
 * @desc Animation ID for Deep Freeze (Ice + Water)
 * @type animation
 * @default 4
 *
 * @param blizzardAnim
 * @text Blizzard Animation
 * @desc Animation ID for Blizzard (Ice + Wind)
 * @type animation
 * @default 5
 *

 *
 * @param stormAnim
 * @text Storm Animation
 * @desc Animation ID for Storm (Lightning + Wind)
 * @type animation
 * @default 7
 *
 * @param moltenRockAnim
 * @text Molten Rock Animation
 * @desc Animation ID for Molten Rock (Fire + Earth)
 * @type animation
 * @default 8
 *
 * @param sandstormAnim
 * @text Sandstorm Animation
 * @desc Animation ID for Sandstorm (Earth + Wind)
 * @type animation
 * @default 9
 *
 * @help ElementalReactions.js
 * 
 * ============================================================================
 * Elemental Reaction System
 * ============================================================================
 * 
 * This plugin automatically triggers special effects when certain elemental
 * states are combined on the same target.
 * 
 * Elemental States (configure these IDs below):
 * - State 35: Burning (Fire)
 * - State 36: Freezing (Ice) 
 * - State 37: Shocking (Lightning)
 * - State 38: Soaking (Water)
 * - State 39: Shatter (Earth)
 * - State 40: Disoriented (Wind)
 * 
 * Reactions:
 * - Fire + Ice = Steam Explosion (removes both states, deals damage)
 * - Fire + Wind = Inferno (spreads burning to nearby enemies)
 * - Lightning + Water = Electrocution (massive damage + chain effect)
 * - Ice + Water = Deep Freeze (immobilizes target)
 * - And more!
 * 
 * ============================================================================
 */

(() => {
    'use strict';
    
    const parameters = PluginManager.parameters('ElementalReactions');
    const reactionDamageMultiplier = Number(parameters['reactionDamageMultiplier'] || 1.5);

    // Animation IDs
    const ANIMATION_IDS = {
        steamExplosion: Number(parameters['steamExplosionAnim'] || 1),
        electrocution: Number(parameters['electrocutionAnim'] || 2),
        inferno: Number(parameters['infernoAnim'] || 3),
        deepFreeze: Number(parameters['deepFreezeAnim'] || 4),
        blizzard: Number(parameters['blizzardAnim'] || 5),

        storm: Number(parameters['stormAnim'] || 7),
        moltenRock: Number(parameters['moltenRockAnim'] || 8),
        sandstorm: Number(parameters['sandstormAnim'] || 9)
    };
    
    // Elemental State IDs
    const ELEMENTAL_STATES = {
        BURNING: 35,      // Fire
        FREEZING: 36,     // Ice
        SHOCKING: 37,     // Lightning
        SOAKING: 38,      // Water
        SHATTER: 39,      // Earth
        DISORIENTED: 40   // Wind
    };

    // Element ID to State mapping (based on your element list)
    const ELEMENT_TO_STATE = {
        2: ELEMENTAL_STATES.BURNING,      // Fire -> Burning
        3: ELEMENTAL_STATES.FREEZING,     // Ice -> Freezing
        4: ELEMENTAL_STATES.SHOCKING,     // Thunder -> Shocking
        5: ELEMENTAL_STATES.SOAKING,      // Water -> Soaking
        6: ELEMENTAL_STATES.SHATTER,      // Earth -> Shatter
        7: ELEMENTAL_STATES.DISORIENTED   // Wind -> Disoriented
    };
    
    // Reaction definitions
    const REACTIONS = {
        // Fire reactions
        [ELEMENTAL_STATES.BURNING]: {
            [ELEMENTAL_STATES.FREEZING]: 'steamExplosion',
            [ELEMENTAL_STATES.DISORIENTED]: 'inferno',
            [ELEMENTAL_STATES.SHATTER]: 'moltenRock'
        },
        
        // Ice reactions
        [ELEMENTAL_STATES.FREEZING]: {
            [ELEMENTAL_STATES.BURNING]: 'steamExplosion',
            [ELEMENTAL_STATES.SOAKING]: 'deepFreeze',
            [ELEMENTAL_STATES.DISORIENTED]: 'blizzard'
        },
        
        // Lightning reactions
        [ELEMENTAL_STATES.SHOCKING]: {
            [ELEMENTAL_STATES.SOAKING]: 'electrocution',
            [ELEMENTAL_STATES.DISORIENTED]: 'storm'
        },
        
        // Water reactions
        [ELEMENTAL_STATES.SOAKING]: {
            [ELEMENTAL_STATES.SHOCKING]: 'electrocution',
            [ELEMENTAL_STATES.FREEZING]: 'deepFreeze'
        },
        
        // Earth reactions
        [ELEMENTAL_STATES.SHATTER]: {
            [ELEMENTAL_STATES.BURNING]: 'moltenRock',
            [ELEMENTAL_STATES.DISORIENTED]: 'sandstorm'
        },
        
        // Wind reactions
        [ELEMENTAL_STATES.DISORIENTED]: {
            [ELEMENTAL_STATES.BURNING]: 'inferno',
            [ELEMENTAL_STATES.FREEZING]: 'blizzard',
            [ELEMENTAL_STATES.SHOCKING]: 'storm',
            [ELEMENTAL_STATES.SHATTER]: 'sandstorm'
        }
    };
    
    // Check for elemental reactions when a state is added
    const _Game_Battler_addState = Game_Battler.prototype.addState;
    Game_Battler.prototype.addState = function(stateId) {
        _Game_Battler_addState.call(this, stateId);

        // Check for reactions after adding the state
        if (Object.values(ELEMENTAL_STATES).includes(stateId)) {
            this.checkElementalReactions(stateId);
        }
    };

    // Hook into action execution to detect elemental spell usage
    const _Game_Action_apply = Game_Action.prototype.apply;
    Game_Action.prototype.apply = function(target) {
        // Store the attacker for damage calculations
        if (target) {
            target._lastAttacker = this.subject();
        }

        // Check for elemental reactions BEFORE applying the action
        this.checkElementalSpellReactions(target);

        // Apply the original action
        _Game_Action_apply.call(this, target);
    };

    // Check if an elemental spell triggers reactions with existing states
    Game_Action.prototype.checkElementalSpellReactions = function(target) {
        if (!this.isSkill()) return;

        const skill = this.item();
        if (!skill) return;

        // Get the element ID of this skill
        const elementId = skill.damage.elementId;
        if (!elementId || elementId === -1) return; // No element or normal attack

        // Check if this element maps to a state
        const newStateId = ELEMENT_TO_STATE[elementId];
        if (!newStateId) return; // Element doesn't map to our reaction states

        // Check if target has compatible states for reactions
        target.checkElementalSpellReaction(newStateId);
    };
    
    // Check for elemental reactions when a new state is added
    Game_Battler.prototype.checkElementalReactions = function(newStateId) {
        const reactions = REACTIONS[newStateId];
        if (!reactions) return;

        // Check each possible reaction - allow multiple reactions per spell
        for (const [otherStateId, reactionType] of Object.entries(reactions)) {
            const otherState = parseInt(otherStateId);
            if (this.isStateAffected(otherState)) {
                // Trigger the reaction
                this.triggerElementalReaction(newStateId, otherState, reactionType);
                // Continue checking for more reactions (no break)
            }
        }
    };

    // Check for elemental reactions when an elemental spell is used
    Game_Battler.prototype.checkElementalSpellReaction = function(incomingStateId) {
        const reactions = REACTIONS[incomingStateId];
        if (!reactions) return;

        // Check each possible reaction with existing states - allow multiple reactions per spell
        for (const [existingStateId, reactionType] of Object.entries(reactions)) {
            const existingState = parseInt(existingStateId);
            if (this.isStateAffected(existingState)) {
                // Trigger the reaction (incoming element + existing state)
                this.triggerElementalReaction(incomingStateId, existingState, reactionType);
                // Continue checking for more reactions (no break)
            }
        }
    };
    
    // Trigger specific elemental reaction and remove involved states
    Game_Battler.prototype.triggerElementalReaction = function(state1, state2, reactionType) {
        // Remove both states involved in the reaction
        this.removeState(state1);
        this.removeState(state2);

        switch (reactionType) {
            case 'steamExplosion':
                this.executeReaction_SteamExplosion(state1, state2);
                break;
            case 'electrocution':
                this.executeReaction_Electrocution(state1, state2);
                break;
            case 'inferno':
                this.executeReaction_Inferno(state1, state2);
                break;
            case 'deepFreeze':
                this.executeReaction_DeepFreeze(state1, state2);
                break;
            case 'blizzard':
                this.executeReaction_Blizzard(state1, state2);
                break;

            case 'storm':
                this.executeReaction_Storm(state1, state2);
                break;
            case 'moltenRock':
                this.executeReaction_MoltenRock(state1, state2);
                break;
            case 'sandstorm':
                this.executeReaction_Sandstorm(state1, state2);
                break;
        }
    };
    
    // Steam Explosion: Fire + Ice
    Game_Battler.prototype.executeReaction_SteamExplosion = function(state1, state2) {
        // States are removed by the trigger function

        // Deal moderate steam damage - scales with MAT and target's MDF
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 50;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 10); // Minimum 10 damage
        this.gainHp(-finalDamage);

        // 🌟 UNIQUE MECHANIC: Steam Healing - Attacker gains HP from steam
        if (this._lastAttacker && this._lastAttacker.isAlive()) {
            const steamHeal = Math.floor(finalDamage * 0.3); // 30% of damage dealt
            this._lastAttacker.gainHp(steamHeal);

            // Show heal popup on attacker
            setTimeout(() => {
                if (this._lastAttacker.showReactionPopup) {
                    this._lastAttacker.showReactionPopup(`+${steamHeal} HP`, '#64ff64', 'steamHeal');
                }
            }, 400);
        }

        // Show damage number immediately after the name (before spell damage)
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff6464', 'steamExplosion');
        }, 200); // Shorter delay so reaction damage appears before spell damage

        // Visual effects
        $gameTemp.requestAnimation([this], ANIMATION_IDS.steamExplosion);

        // Add screen shake for impact
        if ($gameScreen.startShake) {
            $gameScreen.startShake(8, 8, 20); // Moderate shake
        }

        // Show SEXY popup
        this.showReactionPopup('STEAM EXPLOSION!', '#ff6464', 'steamExplosion');
    };
    
    // Electrocution: Lightning + Water
    Game_Battler.prototype.executeReaction_Electrocution = function(state1, state2) {
        // States are removed by the trigger function

        // Deal high electrical damage - water conducts electricity well
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 80;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.4) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 15); // Minimum 15 damage
        this.gainHp(-finalDamage);

        // 🌟 UNIQUE MECHANIC: Electrical Overload - Bypasses all defenses on next hit
        this._electricalOverload = true;
        this._overloadTurns = 2; // Lasts 2 turns

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffff00', 'electrocution');
        }, 200);

        // Visual effects
        $gameTemp.requestAnimation([this], ANIMATION_IDS.electrocution);

        // POWERFUL screen shake for electricity
        if ($gameScreen.startShake) {
            $gameScreen.startShake(12, 12, 25); // Strong electrical shake
        }

        // Show SEXY popup
        this.showReactionPopup('ELECTROCUTION!', '#ffff00', 'electrocution');

        // Show overload effect popup
        setTimeout(() => {
            this.showReactionPopup('DEFENSE BYPASSED!', '#ff00ff', 'overload');
        }, 600);
    };

    
    // Inferno: Fire + Wind (spreads burning)
    Game_Battler.prototype.executeReaction_Inferno = function(state1, state2) {
        // States are removed by the trigger function

        // Inferno intensifies burning - wind fans the flames
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 40;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.6) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 8); // Minimum 8 damage
        this.gainHp(-finalDamage);

        // 🌟 UNIQUE MECHANIC: Blazing Fury - Next attack deals double damage
        if (this._lastAttacker && this._lastAttacker.isAlive()) {
            this._lastAttacker._blazingFury = true;
            this._lastAttacker._furyTurns = 1; // Lasts 1 turn

            // Show fury popup on attacker
            setTimeout(() => {
                if (this._lastAttacker.showReactionPopup) {
                    this._lastAttacker.showReactionPopup('BLAZING FURY!', '#ff6400', 'fury');
                }
            }, 400);
        }

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff3200', 'inferno');
        }, 200);

        // Visual effects
        $gameTemp.requestAnimation([this], ANIMATION_IDS.inferno);

        // Fiery screen shake
        if ($gameScreen.startShake) {
            $gameScreen.startShake(10, 10, 30); // Burning shake
        }

        this.showReactionPopup('INFERNO!', '#ff3200', 'inferno');
    };

    // Deep Freeze: Ice + Water (immobilizes)
    Game_Battler.prototype.executeReaction_DeepFreeze = function(state1, state2) {
        // States are removed by the trigger function

        // Deal ice damage and immobilize - freezing solid
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 30;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.7) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 5); // Minimum 5 damage
        this.gainHp(-finalDamage);

        // 🌟 UNIQUE MECHANIC: Frozen Solid - Cannot act for 1 turn, but takes 50% less damage
        this.addState(4); // Paralysis state
        this._frozenSolid = true;
        this._frozenTurns = 1;

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#64c8ff', 'deepFreeze');
        }, 200);

        // Visual effects
        $gameTemp.requestAnimation([this], ANIMATION_IDS.deepFreeze);

        this.showReactionPopup('DEEP FREEZE!', '#64c8ff', 'deepFreeze');

        // Show frozen effect popup
        setTimeout(() => {
            this.showReactionPopup('FROZEN SOLID!', '#96c8ff', 'frozen');
        }, 600);
    };

    // Blizzard: Ice + Wind (AoE ice damage)
    Game_Battler.prototype.executeReaction_Blizzard = function(state1, state2) {
        // States are removed by the trigger function

        // Blizzard affects only this target - strong ice storm
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 70;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 12); // Minimum 12 damage
        this.gainHp(-finalDamage);

        // 🌟 UNIQUE MECHANIC: Frostbite - Reduces target's ATK and MAT by 25%
        this.addDebuff(2, 3); // ATK debuff for 3 turns
        this.addDebuff(4, 3); // MAT debuff for 3 turns
        this._frostbite = true;
        this._frostbiteTurns = 3;

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#96c8ff', 'blizzard');
        }, 200);

        // Visual effects
        $gameTemp.requestAnimation([this], ANIMATION_IDS.blizzard);

        this.showReactionPopup('BLIZZARD!', '#96c8ff', 'blizzard');

        // Show frostbite effect popup
        setTimeout(() => {
            this.showReactionPopup('FROSTBITE!', '#64c8ff', 'frostbite');
        }, 600);
    };



    // Storm: Lightning + Wind (chain lightning)
    Game_Battler.prototype.executeReaction_Storm = function(state1, state2) {
        // States are removed by the trigger function

        // Storm affects only this target - nature's fury unleashed
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 100;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.3) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 20); // Minimum 20 damage
        this.gainHp(-finalDamage);

        // 🌟 UNIQUE MECHANIC: Storm Surge - Applies AGI debuff and gives attacker AGI buff
        this.addDebuff(6, 5); // AGI debuff for 5 turns

        if (this._lastAttacker && this._lastAttacker.isAlive()) {
            this._lastAttacker.addBuff(6, 3); // AGI buff for 3 turns

            // Show buff popup on attacker
            setTimeout(() => {
                if (this._lastAttacker.showReactionPopup) {
                    this._lastAttacker.showReactionPopup('STORM SURGE!', '#ffff64', 'surge');
                }
            }, 400);
        }

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffff96', 'storm');
        }, 200);

        // Visual effects
        $gameTemp.requestAnimation([this], ANIMATION_IDS.storm);

        this.showReactionPopup('STORM!', '#ffff96', 'storm');
    };

    // Molten Rock: Fire + Earth (terrain damage)
    Game_Battler.prototype.executeReaction_MoltenRock = function(state1, state2) {
        // States are removed by the trigger function

        // Molten rock affects only this target - lava burns intensely
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 55;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 10); // Minimum 10 damage
        this.gainHp(-finalDamage);

        // 🌟 UNIQUE MECHANIC: Molten Armor - Reduces DEF but reflects 25% of physical damage
        this.addDebuff(3, 4); // DEF debuff for 4 turns
        this._moltenArmor = true;
        this._moltenArmorTurns = 4;

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff6400', 'moltenRock');
        }, 200);

        // Add burning DoT effect
        this.addState(7); // Poison/DoT state for molten damage on this target only

        // Visual effects
        $gameTemp.requestAnimation([this], ANIMATION_IDS.moltenRock);

        this.showReactionPopup('MOLTEN ROCK!', '#ff6400', 'moltenRock');

        // Show molten armor effect popup
        setTimeout(() => {
            this.showReactionPopup('MOLTEN ARMOR!', '#ff9600', 'moltenArmor');
        }, 600);
    };

    // Sandstorm: Earth + Wind (AGI debuff + DoT)
    Game_Battler.prototype.executeReaction_Sandstorm = function(state1, state2) {
        // States are removed by the trigger function

        // Sandstorm affects only this target - blinding sand and debris
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 60;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 11); // Minimum 11 damage
        this.gainHp(-finalDamage);

        // 🌟 UNIQUE MECHANIC: Dust Cloud - Reduces accuracy and gives attacker evasion boost
        this.addDebuff(6, 5); // AGI debuff for 5 turns
        this.addState(6); // Blind/accuracy down state

        if (this._lastAttacker && this._lastAttacker.isAlive()) {
            // Temporary evasion boost for attacker
            this._lastAttacker._dustCloudEvasion = true;
            this._lastAttacker._evasionTurns = 2;

            // Show evasion popup on attacker
            setTimeout(() => {
                if (this._lastAttacker.showReactionPopup) {
                    this._lastAttacker.showReactionPopup('DUST CLOUD!', '#c8c864', 'dustCloud');
                }
            }, 400);
        }

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#c89664', 'sandstorm');
        }, 200);

        // Visual effects
        $gameTemp.requestAnimation([this], ANIMATION_IDS.sandstorm);

        this.showReactionPopup('SANDSTORM!', '#c89664', 'sandstorm');
    };

    // Show reaction popup using the built-in VisuMZ system (single method only)
    Game_Battler.prototype.showReactionPopup = function(text, color, reactionType) {
        // Use only ONE method to avoid duplicates
        if (this.setupTextPopup) {
            const popupSettings = {
                textColor: color,
                flashColor: [255, 255, 255, 200],
                flashDuration: 45
            };
            this.setupTextPopup(text, popupSettings);
        } else if (SceneManager._scene && SceneManager._scene._spriteset) {
            // Fallback: try through sprite only if battler method doesn't exist
            const sprites = SceneManager._scene._spriteset.battlerSprites();
            const sprite = sprites.find(s => s._battler === this);
            if (sprite && sprite.setupTextPopup) {
                const popupSettings = {
                    textColor: color,
                    flashColor: [255, 255, 255, 200],
                    flashDuration: 45
                };
                sprite.setupTextPopup(text, popupSettings);
            }
        }
    };







})();
