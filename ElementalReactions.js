/*:
 * @target MZ
 * @plugindesc Elemental Reaction System v1.0.0
 * <AUTHOR>
 * @version 1.0.0
 * @description Automatic elemental reactions when combining elemental states
 *
 * @param ---Fire Reactions---
 * @text ---Fire Reactions---
 * @type string
 * @default
 *
 * @param fireIceReaction
 * @text Fire + Ice Reaction
 * @desc What happens when Fire and Ice states combine
 * @type select
 * @option Steam Explosion
 * @value steamExplosion
 * @option None
 * @value none
 * @default steamExplosion
 *
 * @param fireWindReaction
 * @text Fire + Wind Reaction
 * @desc What happens when Fire and Wind states combine
 * @type select
 * @option Inferno
 * @value inferno
 * @option None
 * @value none
 * @default inferno
 *
 * @param ---Lightning Reactions---
 * @text ---Lightning Reactions---
 * @type string
 * @default
 *
 * @param lightningWaterReaction
 * @text Lightning + Water Reaction
 * @desc What happens when Lightning and Water states combine
 * @type select
 * @option Electrocution
 * @value electrocution
 * @option None
 * @value none
 * @default electrocution
 *
 * @param reactionDamageMultiplier
 * @text Reaction Damage Multiplier
 * @desc Multiplier for reaction damage (1.0 = normal, 2.0 = double)
 * @type number
 * @decimals 1
 * @min 0.1
 * @max 5.0
 * @default 1.5
 *
 * @param ---Animation Settings---
 * @text ---Animation Settings---
 * @type string
 * @default
 *
 * @param steamExplosionAnim
 * @text Steam Explosion Animation
 * @desc Animation ID for Steam Explosion (Fire + Ice)
 * @type animation
 * @default 1
 *
 * @param electrocutionAnim
 * @text Electrocution Animation
 * @desc Animation ID for Electrocution (Lightning + Water)
 * @type animation
 * @default 2
 *
 * @param infernoAnim
 * @text Inferno Animation
 * @desc Animation ID for Inferno (Fire + Wind)
 * @type animation
 * @default 3
 *
 * @param deepFreezeAnim
 * @text Deep Freeze Animation
 * @desc Animation ID for Deep Freeze (Ice + Water)
 * @type animation
 * @default 4
 *
 * @param blizzardAnim
 * @text Blizzard Animation
 * @desc Animation ID for Blizzard (Ice + Wind)
 * @type animation
 * @default 5
 *

 *
 * @param stormAnim
 * @text Storm Animation
 * @desc Animation ID for Storm (Lightning + Wind)
 * @type animation
 * @default 7
 *
 * @param moltenRockAnim
 * @text Molten Rock Animation
 * @desc Animation ID for Molten Rock (Fire + Earth)
 * @type animation
 * @default 8
 *
 * @param sandstormAnim
 * @text Sandstorm Animation
 * @desc Animation ID for Sandstorm (Earth + Wind)
 * @type animation
 * @default 9
 *
 * @help ElementalReactions.js
 * 
 * ============================================================================
 * Elemental Reaction System
 * ============================================================================
 * 
 * This plugin automatically triggers special effects when certain elemental
 * states are combined on the same target.
 * 
 * Elemental States (configure these IDs below):
 * - State 35: Burning (Fire)
 * - State 36: Freezing (Ice) 
 * - State 37: Shocking (Lightning)
 * - State 38: Soaking (Water)
 * - State 39: Shatter (Earth)
 * - State 40: Disoriented (Wind)
 * 
 * Reactions:
 * - Fire + Ice = Steam Explosion (removes both states, deals damage)
 * - Fire + Wind = Inferno (spreads burning to nearby enemies)
 * - Lightning + Water = Electrocution (massive damage + chain effect)
 * - Ice + Water = Deep Freeze (immobilizes target)
 * - And more!
 * 
 * ============================================================================
 */

(() => {
    'use strict';
    
    const parameters = PluginManager.parameters('ElementalReactions');
    const reactionDamageMultiplier = Number(parameters['reactionDamageMultiplier'] || 1.5);

    // Animation IDs
    const ANIMATION_IDS = {
        steamExplosion: Number(parameters['steamExplosionAnim'] || 1),
        electrocution: Number(parameters['electrocutionAnim'] || 2),
        inferno: Number(parameters['infernoAnim'] || 3),
        deepFreeze: Number(parameters['deepFreezeAnim'] || 4),
        blizzard: Number(parameters['blizzardAnim'] || 5),

        storm: Number(parameters['stormAnim'] || 7),
        moltenRock: Number(parameters['moltenRockAnim'] || 8),
        sandstorm: Number(parameters['sandstormAnim'] || 9)
    };
    
    // Elemental State IDs
    const ELEMENTAL_STATES = {
        BURNING: 35,      // Fire
        FREEZING: 36,     // Ice
        SHOCKING: 37,     // Lightning
        SOAKING: 38,      // Water
        SHATTER: 39,      // Earth
        DISORIENTED: 40   // Wind
    };

    // Element ID to State mapping (based on your element list)
    const ELEMENT_TO_STATE = {
        2: ELEMENTAL_STATES.BURNING,      // Fire -> Burning
        3: ELEMENTAL_STATES.FREEZING,     // Ice -> Freezing
        4: ELEMENTAL_STATES.SHOCKING,     // Thunder -> Shocking
        5: ELEMENTAL_STATES.SOAKING,      // Water -> Soaking
        6: ELEMENTAL_STATES.SHATTER,      // Earth -> Shatter
        7: ELEMENTAL_STATES.DISORIENTED   // Wind -> Disoriented
    };
    
    // Reaction definitions
    const REACTIONS = {
        // Fire reactions
        [ELEMENTAL_STATES.BURNING]: {
            [ELEMENTAL_STATES.FREEZING]: 'steamExplosion',
            [ELEMENTAL_STATES.DISORIENTED]: 'inferno',
            [ELEMENTAL_STATES.SHATTER]: 'moltenRock'
        },
        
        // Ice reactions
        [ELEMENTAL_STATES.FREEZING]: {
            [ELEMENTAL_STATES.BURNING]: 'steamExplosion',
            [ELEMENTAL_STATES.SOAKING]: 'deepFreeze',
            [ELEMENTAL_STATES.DISORIENTED]: 'blizzard'
        },
        
        // Lightning reactions
        [ELEMENTAL_STATES.SHOCKING]: {
            [ELEMENTAL_STATES.SOAKING]: 'electrocution',
            [ELEMENTAL_STATES.DISORIENTED]: 'storm'
        },
        
        // Water reactions
        [ELEMENTAL_STATES.SOAKING]: {
            [ELEMENTAL_STATES.SHOCKING]: 'electrocution',
            [ELEMENTAL_STATES.FREEZING]: 'deepFreeze'
        },
        
        // Earth reactions
        [ELEMENTAL_STATES.SHATTER]: {
            [ELEMENTAL_STATES.BURNING]: 'moltenRock',
            [ELEMENTAL_STATES.DISORIENTED]: 'sandstorm'
        },
        
        // Wind reactions
        [ELEMENTAL_STATES.DISORIENTED]: {
            [ELEMENTAL_STATES.BURNING]: 'inferno',
            [ELEMENTAL_STATES.FREEZING]: 'blizzard',
            [ELEMENTAL_STATES.SHOCKING]: 'storm',
            [ELEMENTAL_STATES.SHATTER]: 'sandstorm'
        }
    };
    
    // Check for elemental reactions when a state is added
    const _Game_Battler_addState = Game_Battler.prototype.addState;
    Game_Battler.prototype.addState = function(stateId) {
        _Game_Battler_addState.call(this, stateId);

        // Check for reactions after adding the state
        if (Object.values(ELEMENTAL_STATES).includes(stateId)) {
            this.checkElementalReactions(stateId);
        }
    };

    // Hook into action execution to detect elemental spell usage
    const _Game_Action_apply = Game_Action.prototype.apply;
    Game_Action.prototype.apply = function(target) {
        // Store the attacker for damage calculations
        if (target) {
            target._lastAttacker = this.subject();
        }

        // Check for elemental reactions BEFORE applying the action
        this.checkElementalSpellReactions(target);

        // Apply the original action
        _Game_Action_apply.call(this, target);
    };

    // Check if an elemental spell triggers reactions with existing states
    Game_Action.prototype.checkElementalSpellReactions = function(target) {
        if (!this.isSkill()) return;

        const skill = this.item();
        if (!skill) return;

        // Get the element ID of this skill
        const elementId = skill.damage.elementId;
        if (!elementId || elementId === -1) return; // No element or normal attack

        // Check if this element maps to a state
        const newStateId = ELEMENT_TO_STATE[elementId];
        if (!newStateId) return; // Element doesn't map to our reaction states

        // Check if target has compatible states for reactions
        target.checkElementalSpellReaction(newStateId);
    };
    
    // Check for elemental reactions when a new state is added
    Game_Battler.prototype.checkElementalReactions = function(newStateId) {
        const reactions = REACTIONS[newStateId];
        if (!reactions) return;

        // Check each possible reaction
        for (const [otherStateId, reactionType] of Object.entries(reactions)) {
            const otherState = parseInt(otherStateId);
            if (this.isStateAffected(otherState)) {
                // Trigger the reaction
                this.triggerElementalReaction(newStateId, otherState, reactionType);
                break; // Only one reaction per state addition
            }
        }
    };

    // Check for elemental reactions when an elemental spell is used
    Game_Battler.prototype.checkElementalSpellReaction = function(incomingStateId) {
        const reactions = REACTIONS[incomingStateId];
        if (!reactions) return;

        // Check each possible reaction with existing states
        for (const [existingStateId, reactionType] of Object.entries(reactions)) {
            const existingState = parseInt(existingStateId);
            if (this.isStateAffected(existingState)) {
                // Trigger the reaction (incoming element + existing state)
                this.triggerElementalReaction(incomingStateId, existingState, reactionType);
                break; // Only one reaction per spell
            }
        }
    };
    
    // Trigger specific elemental reaction with cooldown protection
    Game_Battler.prototype.triggerElementalReaction = function(state1, state2, reactionType) {
        // Initialize reaction cooldowns if needed
        if (!this._reactionCooldowns) {
            this._reactionCooldowns = {};
        }

        // Check if this reaction is on cooldown
        const reactionKey = `${state1}_${state2}_${reactionType}`;
        const currentTurn = $gameTroop._turnCount || 0;

        if (this._reactionCooldowns[reactionKey] &&
            this._reactionCooldowns[reactionKey] >= currentTurn) {
            return; // Skip this reaction
        }

        // Set cooldown (1 turn)
        this._reactionCooldowns[reactionKey] = currentTurn + 1;

        switch (reactionType) {
            case 'steamExplosion':
                this.executeReaction_SteamExplosion(state1, state2);
                break;
            case 'electrocution':
                this.executeReaction_Electrocution(state1, state2);
                break;
            case 'inferno':
                this.executeReaction_Inferno(state1, state2);
                break;
            case 'deepFreeze':
                this.executeReaction_DeepFreeze(state1, state2);
                break;
            case 'blizzard':
                this.executeReaction_Blizzard(state1, state2);
                break;

            case 'storm':
                this.executeReaction_Storm(state1, state2);
                break;
            case 'moltenRock':
                this.executeReaction_MoltenRock(state1, state2);
                break;
            case 'sandstorm':
                this.executeReaction_Sandstorm(state1, state2);
                break;
        }
    };
    
    // Steam Explosion: Fire + Ice
    Game_Battler.prototype.executeReaction_SteamExplosion = function(state1, state2) {
        // Keep both states active - reactions are now a bonus, not a punishment!

        // Deal moderate steam damage - scales with MAT and target's MDF
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 50;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 10); // Minimum 10 damage
        this.gainHp(-finalDamage);

        // Show damage number immediately after the name (before spell damage)
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff6464', 'steamExplosion');
        }, 200); // Shorter delay so reaction damage appears before spell damage

        // GORGEOUS visual effects
        $gameScreen.startFlash([100, 200, 255, 200], 30); // Beautiful cyan-blue flash
        this.createReactionParticles('steamExplosion');

        // Add screen shake for impact
        if ($gameScreen.startShake) {
            $gameScreen.startShake(8, 8, 20); // Moderate shake
        }

        // Show SEXY popup
        this.showReactionPopup('STEAM EXPLOSION!', '#ff6464', 'steamExplosion');
    };
    
    // Electrocution: Lightning + Water
    Game_Battler.prototype.executeReaction_Electrocution = function(state1, state2) {
        // Keep both states active - water still conducts, lightning still energizes!

        // Deal high electrical damage - water conducts electricity well
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 80;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.4) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 15); // Minimum 15 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffff00', 'electrocution');
        }, 200);

        // ELECTRIFYING visual effects
        $gameScreen.startFlash([255, 255, 100, 220], 35); // Intense electric flash
        this.createReactionParticles('electrocution');

        // POWERFUL screen shake for electricity
        if ($gameScreen.startShake) {
            $gameScreen.startShake(12, 12, 25); // Strong electrical shake
        }

        // Show SEXY popup
        this.showReactionPopup('ELECTROCUTION!', '#ffff00', 'electrocution');

        // No chaining - reaction only affects this battler
    };

    
    // Inferno: Fire + Wind (spreads burning)
    Game_Battler.prototype.executeReaction_Inferno = function(state1, state2) {
        // Keep both states - wind continues to fan the flames!

        // Inferno intensifies burning - wind fans the flames
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 40;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.6) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 8); // Minimum 8 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff3200', 'inferno');
        }, 200);
        // Keep the burning state and add extra damage over time
        this.addState(ELEMENTAL_STATES.BURNING); // Refresh burning duration

        // BLAZING visual effects
        $gameScreen.startFlash([255, 100, 0, 180], 40); // Intense fire flash
        this.createReactionParticles('inferno');

        // Fiery screen shake
        if ($gameScreen.startShake) {
            $gameScreen.startShake(10, 10, 30); // Burning shake
        }

        this.showReactionPopup('INFERNO!', '#ff3200', 'inferno');
    };

    // Deep Freeze: Ice + Water (immobilizes)
    Game_Battler.prototype.executeReaction_DeepFreeze = function(state1, state2) {
        // Keep both states - frozen water is still ice and water!

        // Deal ice damage and immobilize - freezing solid
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 30;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.7) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 5); // Minimum 5 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#64c8ff', 'deepFreeze');
        }, 200);

        // Add paralysis/immobilize effect (using state 4 as example)
        this.addState(4); // Paralysis state

        // Visual effects
        $gameScreen.startFlash([100, 200, 255, 150], 35); // Ice blue flash
        this.createReactionParticles('deepFreeze');

        this.showReactionPopup('DEEP FREEZE!', '#64c8ff', 'deepFreeze');
    };

    // Blizzard: Ice + Wind (AoE ice damage)
    Game_Battler.prototype.executeReaction_Blizzard = function(state1, state2) {
        // Keep both states - the blizzard continues to rage!

        // Blizzard affects only this target - strong ice storm
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 70;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 12); // Minimum 12 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#96c8ff', 'blizzard');
        }, 200);
        // Add freezing state to this target
        this.addState(ELEMENTAL_STATES.FREEZING);

        // Visual effects
        $gameScreen.startFlash([200, 220, 255, 140], 40); // Light blue flash
        this.createReactionParticles('blizzard');

        this.showReactionPopup('BLIZZARD!', '#96c8ff', 'blizzard');
    };



    // Storm: Lightning + Wind (chain lightning)
    Game_Battler.prototype.executeReaction_Storm = function(state1, state2) {
        // Keep both states - the storm rages on!

        // Storm affects only this target - nature's fury unleashed
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 100;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.3) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 20); // Minimum 20 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ffff96', 'storm');
        }, 200);
        // Intensify shocking state on this target
        this.addState(ELEMENTAL_STATES.SHOCKING);

        // Visual effects
        $gameScreen.startFlash([255, 255, 200, 180], 30); // Bright white-yellow flash
        this.createReactionParticles('storm');

        this.showReactionPopup('STORM!', '#ffff96', 'storm');
    };

    // Molten Rock: Fire + Earth (terrain damage)
    Game_Battler.prototype.executeReaction_MoltenRock = function(state1, state2) {
        // Keep both states - molten rock continues to burn and crumble!

        // Molten rock affects only this target - lava burns intensely
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 55;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 10); // Minimum 10 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#ff6400', 'moltenRock');
        }, 200);

        // Add burning DoT effect
        this.addState(7); // Poison/DoT state for molten damage on this target only

        // Visual effects
        $gameScreen.startFlash([255, 150, 50, 170], 35); // Orange-red flash
        this.createReactionParticles('moltenRock');

        this.showReactionPopup('MOLTEN ROCK!', '#ff6400', 'moltenRock');
    };

    // Sandstorm: Earth + Wind (AGI debuff + DoT)
    Game_Battler.prototype.executeReaction_Sandstorm = function(state1, state2) {
        // Keep both states - the sandstorm continues to swirl!

        // Sandstorm affects only this target - blinding sand and debris
        const baseDamage = (this._lastAttacker ? this._lastAttacker.mat : this.mat) + 60;
        const defense = this.mdf;
        const damage = Math.floor((baseDamage - defense * 0.5) * reactionDamageMultiplier);
        const finalDamage = Math.max(damage, 11); // Minimum 11 damage
        this.gainHp(-finalDamage);

        // Show damage number after a brief delay so it appears under the name
        setTimeout(() => {
            this.showReactionPopup(finalDamage.toString(), '#c89664', 'sandstorm');
        }, 200);

        // Apply AGI debuff
        this.addDebuff(6, 5); // AGI debuff for 5 turns on this target only

        // Visual effects
        $gameScreen.startFlash([200, 150, 100, 130], 45); // Brown/tan flash
        this.createReactionParticles('sandstorm');

        this.showReactionPopup('SANDSTORM!', '#c89664', 'sandstorm');
    };

    // Create reaction-specific particle effects
    Game_Battler.prototype.createReactionParticles = function(reactionType) {
        if (!SceneManager._scene || !SceneManager._scene._spriteset) return;

        const sprites = SceneManager._scene._spriteset.battlerSprites();
        const sprite = sprites.find(s => s._battler === this);
        if (!sprite) return;

        const x = sprite.x;
        const y = sprite.y - sprite.height / 2;

        switch (reactionType) {
            case 'steamExplosion':
                this.createSteamParticles(x, y);
                break;
            case 'electrocution':
                this.createLightningParticles(x, y);
                break;
            case 'inferno':
                this.createFireParticles(x, y);
                break;
            case 'deepFreeze':
                this.createIceParticles(x, y);
                break;
            case 'blizzard':
                this.createSnowParticles(x, y);
                break;
            case 'storm':
                this.createStormParticles(x, y);
                break;
            case 'moltenRock':
                this.createLavaParticles(x, y);
                break;
            case 'sandstorm':
                this.createSandParticles(x, y);
                break;
        }
    };

    // Steam Explosion particles - white/blue vapor bursting outward
    Game_Battler.prototype.createSteamParticles = function(x, y) {
        const scene = SceneManager._scene._spriteset;
        for (let i = 0; i < 15; i++) {
            const particle = new Sprite(new Bitmap(8, 8));
            const ctx = particle.bitmap.context;
            const alpha = 0.7 + Math.random() * 0.3;
            ctx.fillStyle = `rgba(200, 230, 255, ${alpha})`;
            ctx.beginPath();
            ctx.arc(4, 4, 3 + Math.random() * 2, 0, Math.PI * 2);
            ctx.fill();

            particle.x = x + (Math.random() - 0.5) * 20;
            particle.y = y + (Math.random() - 0.5) * 20;
            particle.anchor.set(0.5, 0.5);

            const angle = Math.random() * Math.PI * 2;
            const speed = 2 + Math.random() * 3;
            particle.vx = Math.cos(angle) * speed;
            particle.vy = Math.sin(angle) * speed - 1; // Slight upward bias
            particle.life = 30 + Math.random() * 20;
            particle.maxLife = particle.life;

            scene.addChild(particle);

            // Animate particle
            const animate = () => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.vy += 0.05; // Gravity
                particle.life--;
                particle.opacity = (particle.life / particle.maxLife) * 255;
                particle.scale.x = particle.scale.y = 0.5 + (particle.life / particle.maxLife) * 0.5;

                if (particle.life <= 0) {
                    scene.removeChild(particle);
                    particle.destroy();
                } else {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }
    };

    // Lightning particles - yellow crackling bolts
    Game_Battler.prototype.createLightningParticles = function(x, y) {
        const scene = SceneManager._scene._spriteset;
        for (let i = 0; i < 8; i++) {
            const particle = new Sprite(new Bitmap(20, 4));
            const ctx = particle.bitmap.context;
            ctx.strokeStyle = '#ffff00';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, 2);
            ctx.lineTo(20, 2);
            ctx.stroke();

            particle.x = x;
            particle.y = y;
            particle.anchor.set(0.5, 0.5);
            particle.rotation = Math.random() * Math.PI * 2;
            particle.life = 15 + Math.random() * 10;
            particle.maxLife = particle.life;

            scene.addChild(particle);

            const animate = () => {
                particle.life--;
                particle.opacity = (particle.life / particle.maxLife) * 255;
                particle.rotation += (Math.random() - 0.5) * 0.3;

                if (particle.life <= 0) {
                    scene.removeChild(particle);
                    particle.destroy();
                } else {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }
    };

    // Fire particles - orange/red spiraling upward
    Game_Battler.prototype.createFireParticles = function(x, y) {
        const scene = SceneManager._scene._spriteset;
        for (let i = 0; i < 12; i++) {
            const particle = new Sprite(new Bitmap(6, 6));
            const ctx = particle.bitmap.context;
            const colors = ['#ff3200', '#ff6400', '#ff9600'];
            ctx.fillStyle = colors[Math.floor(Math.random() * colors.length)];
            ctx.beginPath();
            ctx.arc(3, 3, 2 + Math.random(), 0, Math.PI * 2);
            ctx.fill();

            particle.x = x + (Math.random() - 0.5) * 15;
            particle.y = y + (Math.random() - 0.5) * 10;
            particle.anchor.set(0.5, 0.5);

            const angle = Math.random() * Math.PI * 0.5 - Math.PI * 0.25; // Upward cone
            const speed = 1 + Math.random() * 2;
            particle.vx = Math.cos(angle) * speed;
            particle.vy = Math.sin(angle) * speed - 2; // Strong upward bias
            particle.life = 40 + Math.random() * 20;
            particle.maxLife = particle.life;

            scene.addChild(particle);

            const animate = () => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.vx *= 0.98; // Air resistance
                particle.vy -= 0.02; // Rising heat
                particle.life--;
                particle.opacity = (particle.life / particle.maxLife) * 255;
                particle.scale.x = particle.scale.y = 0.3 + (particle.life / particle.maxLife) * 0.7;

                if (particle.life <= 0) {
                    scene.removeChild(particle);
                    particle.destroy();
                } else {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }
    };

    // Ice particles - blue crystals forming and shattering
    Game_Battler.prototype.createIceParticles = function(x, y) {
        const scene = SceneManager._scene._spriteset;
        for (let i = 0; i < 10; i++) {
            const particle = new Sprite(new Bitmap(8, 8));
            const ctx = particle.bitmap.context;
            ctx.fillStyle = '#64c8ff';
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 1;
            // Draw diamond shape
            ctx.beginPath();
            ctx.moveTo(4, 1);
            ctx.lineTo(7, 4);
            ctx.lineTo(4, 7);
            ctx.lineTo(1, 4);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();

            particle.x = x + (Math.random() - 0.5) * 25;
            particle.y = y + (Math.random() - 0.5) * 25;
            particle.anchor.set(0.5, 0.5);
            particle.rotation = Math.random() * Math.PI * 2;
            particle.life = 50 + Math.random() * 30;
            particle.maxLife = particle.life;

            scene.addChild(particle);

            const animate = () => {
                particle.rotation += 0.05;
                particle.life--;
                particle.opacity = (particle.life / particle.maxLife) * 255;

                // Shatter effect in final frames
                if (particle.life < 10) {
                    particle.scale.x = particle.scale.y = (particle.life / 10) * 0.5;
                }

                if (particle.life <= 0) {
                    scene.removeChild(particle);
                    particle.destroy();
                } else {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }
    };

    // Snow particles - white swirling particles
    Game_Battler.prototype.createSnowParticles = function(x, y) {
        const scene = SceneManager._scene._spriteset;
        for (let i = 0; i < 20; i++) {
            const particle = new Sprite(new Bitmap(4, 4));
            const ctx = particle.bitmap.context;
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(2, 2, 1.5, 0, Math.PI * 2);
            ctx.fill();

            const radius = 30 + Math.random() * 20;
            const startAngle = Math.random() * Math.PI * 2;
            particle.centerX = x;
            particle.centerY = y;
            particle.radius = radius;
            particle.angle = startAngle;
            particle.angleSpeed = 0.1 + Math.random() * 0.1;
            particle.x = x + Math.cos(startAngle) * radius;
            particle.y = y + Math.sin(startAngle) * radius;
            particle.anchor.set(0.5, 0.5);
            particle.life = 60 + Math.random() * 40;
            particle.maxLife = particle.life;

            scene.addChild(particle);

            const animate = () => {
                particle.angle += particle.angleSpeed;
                particle.radius *= 0.99; // Spiral inward
                particle.x = particle.centerX + Math.cos(particle.angle) * particle.radius;
                particle.y = particle.centerY + Math.sin(particle.angle) * particle.radius;
                particle.life--;
                particle.opacity = (particle.life / particle.maxLife) * 255;

                if (particle.life <= 0) {
                    scene.removeChild(particle);
                    particle.destroy();
                } else {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }
    };

    // Storm particles - purple/white energy with wind
    Game_Battler.prototype.createStormParticles = function(x, y) {
        const scene = SceneManager._scene._spriteset;
        // Lightning bolts
        for (let i = 0; i < 6; i++) {
            const particle = new Sprite(new Bitmap(15, 3));
            const ctx = particle.bitmap.context;
            ctx.strokeStyle = '#ffff96';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, 1.5);
            ctx.lineTo(15, 1.5);
            ctx.stroke();

            particle.x = x + (Math.random() - 0.5) * 40;
            particle.y = y + (Math.random() - 0.5) * 30;
            particle.anchor.set(0.5, 0.5);
            particle.rotation = Math.random() * Math.PI * 2;
            particle.life = 20 + Math.random() * 15;
            particle.maxLife = particle.life;

            scene.addChild(particle);

            const animate = () => {
                particle.life--;
                particle.opacity = (particle.life / particle.maxLife) * 255;
                particle.rotation += (Math.random() - 0.5) * 0.4;

                if (particle.life <= 0) {
                    scene.removeChild(particle);
                    particle.destroy();
                } else {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }

        // Wind particles
        for (let i = 0; i < 15; i++) {
            const particle = new Sprite(new Bitmap(8, 2));
            const ctx = particle.bitmap.context;
            ctx.fillStyle = 'rgba(200, 200, 255, 0.6)';
            ctx.fillRect(0, 0, 8, 2);

            particle.x = x + (Math.random() - 0.5) * 50;
            particle.y = y + (Math.random() - 0.5) * 40;
            particle.anchor.set(0.5, 0.5);

            const angle = Math.random() * Math.PI * 2;
            const speed = 3 + Math.random() * 2;
            particle.vx = Math.cos(angle) * speed;
            particle.vy = Math.sin(angle) * speed;
            particle.life = 30 + Math.random() * 20;
            particle.maxLife = particle.life;

            scene.addChild(particle);

            const animate = () => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;
                particle.opacity = (particle.life / particle.maxLife) * 255;

                if (particle.life <= 0) {
                    scene.removeChild(particle);
                    particle.destroy();
                } else {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }
    };

    // Lava particles - orange molten drops
    Game_Battler.prototype.createLavaParticles = function(x, y) {
        const scene = SceneManager._scene._spriteset;
        for (let i = 0; i < 10; i++) {
            const particle = new Sprite(new Bitmap(6, 6));
            const ctx = particle.bitmap.context;
            const colors = ['#ff6400', '#ff3200', '#ff9600'];
            ctx.fillStyle = colors[Math.floor(Math.random() * colors.length)];
            ctx.beginPath();
            ctx.arc(3, 3, 2.5, 0, Math.PI * 2);
            ctx.fill();

            particle.x = x + (Math.random() - 0.5) * 20;
            particle.y = y - Math.random() * 10;
            particle.anchor.set(0.5, 0.5);

            const angle = Math.random() * Math.PI * 0.6 + Math.PI * 0.2; // Upward arc
            const speed = 2 + Math.random() * 3;
            particle.vx = Math.cos(angle) * speed;
            particle.vy = Math.sin(angle) * speed;
            particle.life = 45 + Math.random() * 25;
            particle.maxLife = particle.life;

            scene.addChild(particle);

            const animate = () => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.vy += 0.15; // Gravity
                particle.life--;
                particle.opacity = (particle.life / particle.maxLife) * 255;

                if (particle.life <= 0) {
                    scene.removeChild(particle);
                    particle.destroy();
                } else {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }
    };

    // Sand particles - brown swirling dust
    Game_Battler.prototype.createSandParticles = function(x, y) {
        const scene = SceneManager._scene._spriteset;
        for (let i = 0; i < 25; i++) {
            const particle = new Sprite(new Bitmap(3, 3));
            const ctx = particle.bitmap.context;
            const colors = ['#c89664', '#d4a574', '#b8864a'];
            ctx.fillStyle = colors[Math.floor(Math.random() * colors.length)];
            ctx.fillRect(0, 0, 3, 3);

            const radius = 20 + Math.random() * 30;
            const startAngle = Math.random() * Math.PI * 2;
            particle.centerX = x;
            particle.centerY = y;
            particle.radius = radius;
            particle.angle = startAngle;
            particle.angleSpeed = 0.15 + Math.random() * 0.1;
            particle.x = x + Math.cos(startAngle) * radius;
            particle.y = y + Math.sin(startAngle) * radius;
            particle.anchor.set(0.5, 0.5);
            particle.life = 50 + Math.random() * 30;
            particle.maxLife = particle.life;

            scene.addChild(particle);

            const animate = () => {
                particle.angle += particle.angleSpeed;
                particle.x = particle.centerX + Math.cos(particle.angle) * particle.radius;
                particle.y = particle.centerY + Math.sin(particle.angle) * particle.radius;
                particle.life--;
                particle.opacity = (particle.life / particle.maxLife) * 255;

                if (particle.life <= 0) {
                    scene.removeChild(particle);
                    particle.destroy();
                } else {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }
    };

    // Show reaction popup using the built-in VisuMZ system (single method only)
    Game_Battler.prototype.showReactionPopup = function(text, color, reactionType) {
        // Use only ONE method to avoid duplicates
        if (this.setupTextPopup) {
            const popupSettings = {
                textColor: color,
                flashColor: [255, 255, 255, 200],
                flashDuration: 45
            };
            this.setupTextPopup(text, popupSettings);
        } else if (SceneManager._scene && SceneManager._scene._spriteset) {
            // Fallback: try through sprite only if battler method doesn't exist
            const sprites = SceneManager._scene._spriteset.battlerSprites();
            const sprite = sprites.find(s => s._battler === this);
            if (sprite && sprite.setupTextPopup) {
                const popupSettings = {
                    textColor: color,
                    flashColor: [255, 255, 255, 200],
                    flashDuration: 45
                };
                sprite.setupTextPopup(text, popupSettings);
            }
        }
    };





    // Clear reaction cooldowns when battle ends
    const _BattleManager_endBattle = BattleManager.endBattle;
    BattleManager.endBattle = function(result) {
        // Clear all reaction cooldowns for all battlers
        $gameParty.allMembers().forEach(actor => {
            if (actor._reactionCooldowns) {
                actor._reactionCooldowns = {};
            }
        });

        $gameTroop.members().forEach(enemy => {
            if (enemy._reactionCooldowns) {
                enemy._reactionCooldowns = {};
            }
        });

        _BattleManager_endBattle.call(this, result);
    };

})();
